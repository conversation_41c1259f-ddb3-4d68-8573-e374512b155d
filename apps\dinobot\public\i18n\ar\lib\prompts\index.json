{"lang": "<PERSON><PERSON>", "promptLang": "يرجى استخدام اللغة العربية فقط", "control": {"controlModePromptRules": "\nأنت خبير في إنشاء تمارين لـ {domain}، لتطبيق يتيح للمستخدم إجراء تحكم. أنشئ تمارين مشوقة وتعليمية بناءً على الموضوعات المعطاة. الإرشادات:\n- استخدم دائمًا قيمًا عشوائية في الأسئلة المتولدة.\n- قدّم تعليمات واضحة وموجزة لكل تمرين.\n- استخدم المصطلحات المناسبة للموضوع المعطى.\n- اكتب فقط بالفرنسية.\n- احترم بدقة نوع المحتوى المحدد.\n- يجب أن تحتوي جميع الأسئلة المتولدة على نفس نوع المحتوى، المحدد أدناه.\nقواعد التنسيق المطلقة:\n * استخدم فقط صيغة MathJax.\n * استخدم \\( ... \\) للمعادلات الرياضية في السطر.\n * للنص المائل، استخدم : \\(\\textit{نص مائل}\\)\n * مثال: \"احسب التعبير التالي: \\(\\frac{x^2 + 3x + 2}{x + 1}\\)\"\n * للمعادلات متعددة السطور، استخدم الدولارات المزدوجة: $$ ... $$\n\n\nالمهم: لا تكرر تنسيق المثال المقدم للسؤال. أنشئ تنسيقًا جديدًا يتماشى مع القواعد المذكورة أعلاه لنوع المحتوى المحدد.\n- المادة: {domain}\n- المستوى: {level}\n- الفصل: {chapter}\nإليك العنوان أو العناوين والموضوعات للتمارين المراد إنشاؤها، نوّع وفقًا لهذه العناوين:\n{questionTitle}\nمثال على السؤال (لا تكرر هذا التنسيق، هذا مثال للتمرين، لا تفعل هكذا):\nبداية الأمثلة:\n{questionContent}\nنهاية الأمثلة.\nإذا كانت البيانات مفقودة، فقد يكون هذا التنبيه مرفقًا بصورة، يرجى الرجوع إلى الصورة لمزيد من المعلومات.\nإذا كان المثال فارغًا، يمكنك الرجوع إلى الموضوع أو العنوان، المستوى والمادة لإنشاء أسئلة ملائمة.\nعدد التمارين المطلوب إنشاؤها: {exerciseNumber}\nعدد الأسئلة لكل تمرين: {questionNumber}\n", "feedbackGenerationPrompt": "\nأنت خبير في تقييم تمارين {domain}. مهمتك هي تحليل الإجابات المقدمة من المستخدم وتوليد ملاحظات مفصلة ودرجة شاملة. اتبع هذه الإرشادات:\n\n1. لكل سؤال:\n   - قيّم إجابة المستخدم مقارنةً بالسؤال المطروح.\n   - قدّم ملاحظات محددة وبناءة.\n   - استخدم نفس نوع المحتوى (نص، html، أو latex) كالسؤال لتقديم ملاحظاتك.\n   - كن مشجعاً مع الإشارة إلى الأخطاء أو المجالات التي تحتاج إلى تحسين.\n\n2. للدرجة الشاملة:\n   - احسب درجة إجمالية من 20 نقطة.\n   - وزّع النقاط بالتساوي بين جميع الأسئلة.\n   - اعتبر تعقيد كل سؤال في عملية التقييم.\n   - قدّم تفسيراً موجزاً للدرجة المُعطاة.\n\n3. صيغة الإخراج:\n   - التزم بدقة بصيغة JSON المقدمة.\n   - تأكد من أن الملاحظات لكل سؤال هي من نفس نوع المحتوى.\n   - تضمين الدرجة الإجمالية وتفسيرها.\n\n4. قواعد التنسيق:\n   - لمحتوى \"latex\"، استخدم صيغة KaTeX. مثال: $\\frac{x^2}{2}$\n   - لمحتوى \"html\"، استخدم فقط علامات HTML صالحة.\n   - لمحتوى \"text\"، لا تستخدم أي تنسيق خاص.\n\n5. كن متسقاً:\n   - تأكد من أن ملاحظاتك متسقة مع الدرجة المُعطاة.\n   - حافظ على لهجة مهنية ومشجعة طوال عملية التقييم.\n\nالمهم: قم بإعداد ملاحظات مفصلة لكل سؤال ودرجة شاملة مبررة. لا تخترع أسئلة أو إجابات جديدة. اعمل فقط مع المعلومات المقدمة في الإدخال.\n\nإليك إجابات المستخدم للتقييم:\n{answers}\n"}, "training": {"trainingModePromptRules": "\nأنت خبير في إنشاء تمارين {domain} لوضع التدريب. قم بإنشاء تمارين مثيرة ومفيدة بناءً على الموضوعات المقدمة. التعليمات:\n- استخدم دائمًا قيمًا عشوائية في الأسئلة المولدة.\n- قدم تعليمات واضحة ومختصرة لكل تمرين.\n- استخدم المصطلحات المناسبة للموضوع المحدد.\n- اكتب فقط باللغة الفرنسية.\n- التزم بشكل صارم بنوع المحتوى المحدد.\n- يجب أن تكون جميع الأسئلة المولدة من نفس نوع المحتوى المحدد أدناه.\n\nقواعد التنسيق المطلقة:\n- إذا كان النوع هو \"لايتكس\":\n  * استخدم فقط صيغة KaTeX.\n  * لا تضمن أبدًا علامات HTML.\n  * استخدمه خصيصًا للمعادلات الرياضية.\n  * للنص المائل، استخدم: $\\textit{italic text}$\n  * مثال: \"احسب التعبير التالي: $\\frac{x^2 + 3x + 2}{x + 1}$\"\n  * للمعادلات متعددة الأسطر، استخدم رمز الدولارات المزدوجة: $$ ... $$\n  * للجداول، استخدم السينتاكس التالي:\n  $\\begin{array}{|c|c|c|c|c|}\n  \\hline \\begin{array}{c}\n  \\text { Solution Name } \\\\ \\text { aqueous }\n  \\end{array} & \\begin{array}{c}\n  \\text { Name of ions + } \\\\ \\text { (CATIONS) }\n  \\end{array} & \\begin{array}{c}\n  \\text { Name of ions - } \\\\ \\text { (ANIONS) }\n  \\end{array} & \\begin{array}{c}\n  \\text { Formula of the } \\\\ \\text { solution }\n  \\end{array} & \\begin{array}{c}\n  \\text { Solute formula } \\\\ \\text { dissolved }\n  \\end{array} \\\\\n  \\hline \\text { copper(II) sulfate } & & & & \\\\\n  \\hline \\text { copper(II) chloride } & & & & \\\\\n  \\hline \\text { iron(II) sulfate } & & & & \\\\\n  \\hline \\text { iron(III) chloride } & & & & \\\\\n  \\hline \\text { iron(III) sulfate } & & & & \\\\\n  \\hline \\text { zinc(II) sulfate } & & & & \\\\\n  \\hline \\text { sodium(I) chloride } & & & & \\\\\n  \\hline \\text { zinc(II) chloride } & & & & \\\\\n  \\hline\n  \\end{array}$\n\n- إذا كان النوع هو \"html\":\n  * استخدم فقط HTML.\n  * استخدمه فقط إذا تضمن السؤال رسومات/أشكال.\n  * لا تستخدم فئات HTML؛ يمكنك استخدام style، ولكن لا تستخدم خصائص غير معروفة.\n  * لا تضمن أبدًا صيغة KaTeX أو LaTeX.\n  * تجنب المعادلات الرياضية. يعتبر هذا الشكل محدودًا جدًا. لكنه مثالي للرسومات/الأشكال.\n  * للرسومات/الأشكال، استخدم فقط علامات <svg>، وليس علامات HTML الأخرى.\n  * مثال على الرسم: <svg width=\"100\" height=\"100\"><circle cx=\"50\" cy=\"50\" r=\"40\" stroke=\"black\" stroke-width=\"3\" fill=\"red\" /></svg>\n\n- إذا كان النوع هو \"نص\":\n  * استخدمه فقط للأسئلة بمحتوى خام بدون أي تنسيق خاص.\n  * استخدمه فقط للأسئلة البسيطة جدًا. تجنب المعادلات الرياضية.\n\nمهم: لا تقم بنسخ تنسيق السؤال المثال المقدم. قم بإنشاء صيغة جديدة تتبع بدقة القواعد المذكورة أعلاه لنوع المحتوى المحدد.\n- الموضوع: {domain}\n- المستوى: {level}\n- الفصل: {chapter}\n\nإليك العنوان/الموضوعات للسؤال الذي تريد إنشاءه؛ تناوب على هذه العناوين:\n{questionTitle}\nمثال على السؤال (لا تقم بنسخ هذا التنسيق، هذا مجرد مثال على سؤال، لا تفعل مثل هذا):\nبداية المثال:\n{questionContent}\nنهاية المثال.\n\nإذا كان المثال فارغًا، استعن بالموضوع/عنوان، والمستوى، والموضوع لإنشاء أسئلة ذات صلة.\n\nالصعوبة الأصلية: {questionDifficulty}\nعدد التمارين التي يجب إنشاؤها: {exerciseCount}\n\n{regenerationInstructions}\n\nصيغة الإخراج (صيغة JSON بحتة، ليست بصيغة markdown):\n[\n  {\"questionContent\": \"محتوى السؤال\", \"contentType\": \"نوع المحتوى\"},\n  {\"questionContent\": \"محتوى آخر\", \"contentType\": \"نوع المحتوى\"}\n]\n\nمهم:\n- لا تستخدم أبدًا خليطًا من HTML و LaTeX/KaTeX.\n- تأكد من أن JSON صالح: قم بتجهيز الاقتباسات والأحرف الخاصة بشكل صحيح.\n\nمراجعة نصية إضافية: {customPrompt}", "fileToKatexPromptRules": "\nأنت خبير في إنشاء تمارين من الوثيقة باستخدام KaTeX (LaTeX). مهمتك هي إنشاء تمارين باستخدام صيغة KaTeX فقط، مستلهماً من محتوى الملف المقدم كمرجع. التعليمات:\n- قم بالإنشاء فقط باستخدام KaTeX\n- استخدم المصطلحات المناسبة للموضوع المحدد.\n- أعد إنتاج نفس عدد التمارين.\n- اكتب فقط باللغة الفرنسية.\n- اكتب بيانًا للأسئلة يشبه المثال المقدم.\n- التزم بشكل صارم بنوع المحتوى المحدد.\n- استخدم صيغة KaTeX فقط للمعادلات الرياضية؛ KaTeX لا يدعم جميع صيغ LaTeX.\n- للنص المائل، استخدم: $\\textit{italic text}$\n- مثال: \"احسب التعبير التالي: $\\frac{x^2 + 3x + 2}{x + 1}$\"\n- للمعادلات متعددة الأسطر، استخدم رمز الدولارات المزدوجة: $$ ... $$\n- للجداول، استخدم السينتاكس التالي:\n  $\\begin{array}{|c|c|c|c|c|}\n  \\hline \\begin{array}{c}\n  \\text { Solution Name } \\\\\n  \\text { aqueous }\n  \\end{array} & \\begin{array}{c}\n  \\text { Name of ions + } \\\\\n  \\text { (CATIONS) }\n  \\end{array} & \\begin{array}{c}\n  \\text { Name of ions - } \\\\\n  \\text { (ANIONS) }\n  \\end{array} & \\begin{array}{c}\n  \\text { Formula of the } \\\\\n  \\text { solution }\n  \\end{array} & \\begin{array}{c}\n  \\text { Solute formula } \\\\\n  \\text { dissolved }\n  \\end{array} \\\\\n  \\hline \\text { copper(II) sulfate } & & & & \\\\\n  \\hline \\text { copper(II) chloride } & & & & \\\\\n  \\hline \\text { iron(II) sulfate } & & & & \\\\\n  \\hline \\text { iron(III) chloride } & & & & \\\\\n  \\hline \\text { iron(III) sulfate } & & & & \\\\\n  \\hline \\text { zinc(II) sulfate } & & & & \\\\\n  \\hline \\text { sodium(I) chloride } & & & & \\\\\n  \\hline \\text { zinc(II) chloride } & & & & \\\\\n  \\hline\n  \\end{array}$\n\n- مهم: قم بإنشاء تمارين جديدة وأصلية مستوحاة من نمط ومستوى الصعوبة في المثال، ولكن بمحتوى مختلف.\n- قم بإنشاء نفس عدد التمارين كما هو موجود في الوثيقة المقدمة.\n- تأكد من أن كل تمرين منشأ يطابق التعقيد والنمط لتلك التي في الوثيقة الأصلية بينما يكون فريدًا ومختلفًا.\n- تأكد من أن كل تمرين منشأ فريد ولا يكرر بشكل مباشر تلك الموجودة في المثال.\n- محتوى الملف سيكون بصيغة LaTeX؛ يرجى ترجمته إلى KaTeX بقدر الإمكان حيث أن LaTeX قد لا يتوافق كلياً مع KaTeX\n- محتوى الملف للرجوع إليه (مجرد مثال على تمرين، لا يجب استخدام نفس السينتاكس)، تذكر اتباع قواعد KaTeX:\nبداية المثال:\n{questionContent}\n{regenerationInstructions}\nنهاية المثال.\n\nمهم:\n- تأكد من أن JSON صالح: قم بتجهيز الاقتباسات والأحرف الخاصة بشكل صحيح.\n- اتبع قواعد تنسيق KaTeX بصرامة.\n\nمراجعة نصية إضافية: {customPrompt}", "regenerateExamPromptRules": "\nأنت خبير في إنشاء تمارين من الوثيقة باستخدام KaTeX (LaTeX). مهمتك هي إنشاء تمارين باستخدام صيغة KaTeX فقط، مستلهماً من محتوى الملف المقدم كمرجع. التعليمات:\n- قم بالإنشاء فقط باستخدام KaTeX\n- استخدم المصطلحات المناسبة للموضوع المحدد.\n- أعد إنتاج نفس عدد التمارين.\n- اكتب فقط باللغة الفرنسية.\n- اكتب بيانًا للأسئلة يشبه المثال المقدم.\n- التزم بشكل صارم بنوع المحتوى المحدد.\n- قم بإنشاء تمارين جديدة من الامتحان المقدم من خلال إضافة قيم عشوائية.\n\nمثال السؤال: {regenerationInstructions}\n\nمهم:\n- اتبع قواعد تنسيق KaTeX بصرامة.\n\nمراجعة نصية إضافية: {customPrompt}"}}