import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useSearchParams } from 'react-router-dom'
import { useNavigate } from 'react-router-dom'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@dinobot/components-ui'
import AboutTab from './tabs/AboutTab'
// import StudentsTab from './StudentsTab'
// import CalendarTab from './CalendarTab'
// import AppreciationsTab from './AppreciationsTab'
// import ExamTab from './ExamTab'
// import ScoringTab from './ScoringTab'
import { useClassDetailsStore } from '../stores/ClassDetails.store'
import { useClassDetails } from '../hooks/useClassDetails'

interface ClassDetailsFormProps {
  classId: string
  flagAppreciations: boolean
}

const ClassDetailsForm: React.FC<ClassDetailsFormProps> = ({ 
  classId, 
  flagAppreciations 
}) => {
  const { t, i18n } = useTranslation(['teacher/my-class/apropo'])
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const dir = i18n.dir()
  
  const { 
    setCurrentClass, 
    setFeatureFlag, 
    featureFlag 
  } = useClassDetailsStore()

  const { data: classData, isLoading } = useClassDetails(classId)

  useEffect(() => {
    setFeatureFlag(flagAppreciations)
    if (classData) {
      setCurrentClass(classData)
    }
  }, [classData, flagAppreciations, setCurrentClass, setFeatureFlag])

  const activeTab = searchParams.get('tab') || 'about'

  const handleTabChange = (value: string) => {
    setSearchParams({ tab: value })
  }

  if (isLoading) {
    return <div>Loading...</div>
  }

  return (
    <div className="flex h-full gap-2">
      <div className="w-full bg-white h-full">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="h-full"
        >
          <div className="w-full h-16 inline-flex items-center justify-between rounded-lg p-1 text-muted-foreground">
            <TabsList
              className={`w-full h-16 gap-2 ${
                dir === 'rtl' ? 'flex-row-reverse' : ''
              } justify-start`}
            >
              <TabsTrigger
                value="about"
                className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
              >
                {t('tabs.about')}
              </TabsTrigger>
              <TabsTrigger
                value="students"
                className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
              >
                {t('tabs.students')}
              </TabsTrigger>
              <TabsTrigger
                value="calendar"
                className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
              >
                {t('tabs.calendar')}
              </TabsTrigger>
              <TabsTrigger
                value="exam"
                className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
              >
                {t('tabs.exam')}
              </TabsTrigger>
              <TabsTrigger
                value="scoring"
                className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
              >
                {t('tabs.scoring')}
              </TabsTrigger>
              {featureFlag && (
                <TabsTrigger
                  value="appreciations"
                  className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                  {t('tabs.appreciation')}
                </TabsTrigger>
              )}
            </TabsList>
          </div>
          
          <TabsContent value="about">
            <AboutTab  />
          </TabsContent>
          {/* <TabsContent value="students">
            <StudentsTab classId={classId} />
          </TabsContent>
          <TabsContent value="calendar">
            <CalendarTab classId={classId} />
          </TabsContent>
          <TabsContent value="exam">
            <ExamTab classId={classId} />
          </TabsContent>
          <TabsContent value="scoring" className="h-[calc(100%-75px)]">
            <ScoringTab classId={classId} />
          </TabsContent>
          {featureFlag && (
            <TabsContent value="appreciations">
              <AppreciationsTab classId={classId} />
            </TabsContent>
          )} */}
        </Tabs>
      </div>
    </div>
  )
}

export default ClassDetailsForm