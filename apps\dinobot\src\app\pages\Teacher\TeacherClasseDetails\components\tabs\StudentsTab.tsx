import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@dinobot/components-ui'
import { cn } from '@dinobot/utils'
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable
} from '@tanstack/react-table'
import { ChevronDown, ChevronUp, User } from 'lucide-react'
import { useClassDetailsStore } from '../../stores/ClassDetails.store'
import { useClassStudents } from '../../hooks/useClassDetails'
import AddStudent from './AddStudent'
import StudentMenu from './StudentMenu'

interface StudentsTabProps {
  classId: string
}

interface Student {
  id: string
  firstName: string
  lastName: string
  classMedian?: number
  totalMedian?: number
  email?: string
}

const StudentsTab: React.FC<StudentsTabProps> = ({ classId }) => {
  const { t, i18n } = useTranslation('teacher-class-details')
  const dir = i18n.dir()
  const { students, setStudents } = useClassDetailsStore()
  const { data: studentsData, isLoading } = useClassStudents(classId)

  useEffect(() => {
    if (studentsData) {
      setStudents(studentsData)
    }
  }, [studentsData, setStudents])

  const columns: ColumnDef<Student>[] = [
    {
      accessorKey: 'id'
    },
    {
      header: () => (
        <h2>
          {students.length} {t('students.title')}
        </h2>
      ),
      id: 'avatar',
      cell: () => {
        return (
          <div className={cn('flex justify-start', dir === 'rtl' && 'flex-row-reverse')}>
            <User className={cn('rounded-full')} />
          </div>
        )
      }
    },
    {
      accessorFn: (student) => [student.firstName, student.lastName].join(' '),
      header: '',
      enableSorting: false,
      id: 'name',
      cell: ({ row }) => {
        const value: string = row.getValue('name')
        return (
          <div className={cn('flex justify-start', dir === 'rtl' && 'flex-row-reverse')}>
            <span>{value}</span>
          </div>
        )
      }
    },
    {
      header: t('students.average'),
      accessorFn: (student) => student.classMedian,
      id: 'classMedian',
      cell: ({ row }) => {
        const value: number = row.getValue('classMedian')
        return <span>{value || '-'}</span>
      }
    },
    {
      header: t('students.progress'),
      accessorFn: (student) => student.totalMedian,
      id: 'totalMedian',
      cell: ({ row }) => {
        const value: string = row.getValue('totalMedian')
        return <span>{value || '-'}</span>
      }
    },
    {
      accessorFn: (student) => student.id,
      header: () => <AddStudent classId={classId} />,
      enableSorting: false,
      id: 'actions',
      cell: ({ row }) => {
        const id: string = row.getValue('actions')
        return (
          <div className={cn('flex justify-end', dir === 'rtl' && 'flex-row-reverse')}>
            <StudentMenu classId={classId} studentId={id} />
          </div>
        )
      }
    }
  ]

  const table = useReactTable({
    data: students,
    columns: dir === 'rtl' ? columns.reverse() : columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility: { id: false }
    }
  })

  if (isLoading) {
    return <div>{t('status.loading')}</div>
  }

  return (
    <div>
      <Table className="overflow-y-auto custom-scroller">
        <TableHeader className="shadow-none">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header, i) => (
                <TableHead
                  key={header.id}
                  className={header.column.getCanSort() ? 'cursor-pointer select-none' : ''}
                  title={
                    header.column.getCanSort()
                      ? header.column.getNextSortingOrder() === 'asc'
                        ? 'Sort ascending'
                        : header.column.getNextSortingOrder() === 'desc'
                          ? 'Sort descending'
                          : 'Clear sort'
                      : undefined
                  }
                >
                  <div className="w-full flex flex-col justify-start items-center font-semibold">
                    <div
                      className={`w-full flex ${
                        i === headerGroup.headers.length - 1 ? 'justify-end' : 'justify-start'
                      } items-center gap-1`}
                      onClick={header.column.getToggleSortingHandler()}
                    >
                      <div>
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </div>
                      <div>
                        {{
                          asc: <ChevronUp />,
                          desc: <ChevronDown />
                        }[header.column.getIsSorted() as string] ?? null}
                      </div>
                    </div>
                  </div>
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && 'selected'}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                {t('students.empty')}
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}

export default StudentsTab