import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

interface ClassData {
  id: string
  name: string
  classColor?: string
  students: Array<{
    id: string
    firstName: string
    lastName: string
    email?: string
  }>
  mainTeacher?: {
    id: string
    firstName: string
    lastName: string
  }
  scholarYear: string
  levelId: string
}

interface Student {
  id: string
  firstName: string
  lastName: string
  email?: string
  average?: number
}

interface ClassDetailsState {
  // State
  currentClass: ClassData | null
  students: Student[]
  featureFlag: boolean
  selectedTab: string
  isLoading: boolean
  error: string | null

  // Actions
  setCurrentClass: (classData: ClassData | null) => void
  setStudents: (students: Student[]) => void
  setFeatureFlag: (flag: boolean) => void
  setSelectedTab: (tab: string) => void
  setIsLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Computed getters
  getStudentCount: () => number
  getClassColor: () => string
  
  // Reset
  resetStore: () => void
}

const initialState = {
  currentClass: null,
  students: [],
  featureFlag: false,
  selectedTab: 'about',
  isLoading: false,
  error: null,
}

export const useClassDetailsStore = create<ClassDetailsState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Actions
      setCurrentClass: (classData) => 
        set((state) => ({ 
          ...state, 
          currentClass: classData,
          students: classData?.students || []
        }), false, 'setCurrentClass'),

      setStudents: (students) => 
        set((state) => ({ ...state, students }), false, 'setStudents'),

      setFeatureFlag: (flag) => 
        set((state) => ({ ...state, featureFlag: flag }), false, 'setFeatureFlag'),

      setSelectedTab: (tab) => 
        set((state) => ({ ...state, selectedTab: tab }), false, 'setSelectedTab'),

      setIsLoading: (loading) => 
        set((state) => ({ ...state, isLoading: loading }), false, 'setIsLoading'),

      setError: (error) => 
        set((state) => ({ ...state, error }), false, 'setError'),

      // Computed getters
      getStudentCount: () => {
        const { students } = get()
        return students.length
      },

      getClassColor: () => {
        const { currentClass } = get()
        return currentClass?.classColor || '#3B82F6'
      },

      // Reset
      resetStore: () => 
        set(() => ({ ...initialState }), false, 'resetStore'),
    }),
    {
      name: 'class-details-store',
    }
  )
)