import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useSignup } from '../hooks/useSignup';
import { SignupFormData, RegistrationType } from '../signup.types';
import { Link } from 'react-router-dom';

interface SignupFormProps {
  registrationType: RegistrationType;
}

interface LoginButtonProps {
  acceptedTerms: boolean;
}

const LoginButton: React.FC<LoginButtonProps> = ({ acceptedTerms }) => {
  const { t } = useTranslation(['app/auth'])
  const { isSignupLoading } = useSignup();

  return (
    <button
      type="submit"
      disabled={!acceptedTerms || isSignupLoading}
      className={`flex flex-row justify-center items-center my-2 h-10 w-32  bg-dinoBotVibrantBlue text-white rounded-xl border  hover:text-white transition-all duration-300 ${acceptedTerms ? '' : 'opacity-30'}`}
      aria-disabled={isSignupLoading || !acceptedTerms}
    >
      {isSignupLoading ? (
        <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : (
        t('signup.step.submit')
      )}
    </button>
  );
};

interface PasswordVisibilityProps {
  passwordVisibility: boolean;
  showPassword: () => void;
  hidePassword: () => void;
}

const PasswordVisibility: React.FC<PasswordVisibilityProps> = ({
  passwordVisibility,
  showPassword,
  hidePassword
}) => {
  return (
    <button
      type="button"
      className="absolute inset-y-0 right-0 pr-3 flex items-center"
      onClick={passwordVisibility ? hidePassword : showPassword}
    >
      {passwordVisibility ? (
        <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m-3-3l6.364 6.364" />
        </svg>
      ) : (
        <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      )}
    </button>
  );
};

const SignupForm: React.FC<SignupFormProps> = ({ registrationType }) => {
  const { t } = useTranslation(['app/auth'])
  const { signup, resendEmail, isResendLoading } = useSignup();

  const [formData, setFormData] = useState<SignupFormData>({
    email: '',
    password: '',
    password2: '',
    type: 'student'
  });

  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [showPassword, setShowPassword] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);
  const [canResend, setCanResend] = useState(false);
  const [timer, setTimer] = useState(30);

  useEffect(() => {
    if (!isRegistered) return;

    const interval = setInterval(() => {
      setTimer(prevTimer => prevTimer - 1);
    }, 1000);

    if (timer === 0) {
      setCanResend(true);
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [timer, isRegistered]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      type: e.target.value as 'student' | 'teacher'
    }));
  };

  const handleShowPassword = (setShowPasswordState: React.Dispatch<React.SetStateAction<boolean>>) =>
    (currentState: boolean) => {
      setShowPasswordState(!currentState);
    };

  const handleResendEmail = async () => {
    try {
      await resendEmail(userEmail);
      setCanResend(false);
      setTimer(30);
    } catch (error) {
      console.error('Failed to resend email:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!acceptedTerms) return;

    try {
      const result = await signup(formData);
      setIsRegistered(true);
      setUserEmail(result.email || formData.email);
      setTimer(30);
      setCanResend(false);
    } catch (error) {
      console.error('Signup failed:', error);
    }
  };

  const handleCheckChange = () => {
    setAcceptedTerms(!acceptedTerms);
  };

  if (isRegistered) {
    return (
      <div
        className=" relative bg-white size-full flex flex-col gap-11 justify-evenly items-center  pb-10"
        style={{ backgroundImage: "url('/bg-with-icons.webp')" }}
      >
        <img
          src={'/auth-bg-icons.webp'}
          alt="oui active"
          width={908}
          height={465}
          className="absolute  md:block  sm:block top-0 left-1/2 -translate-x-1/2"
        />
        {/* <div className=' absolute top-0 left-0 w-full  h-[465px] bg-no-repeat object-contain bg-top' style={{ backgroundImage: "url('/auth-bg-icons.webp')" }}></div> */}
        <div className="mt-10">
          <img
            src={'/oui-active.svg'}
            alt="oui active"
            width={322}
            height={55}
          />
        </div>
        <div className="flex flex-col items-center gap-4 space-y-3 animate-fade-in-down ">
          <div
            className="w-[95%] sm:w-3/4 flex flex-col flex-1 justify-center items-center rounded-lg border  px-6 pb-4 pt-8 shadow-md dark:bg-zinc-950 md:w-1/2 bg-clip-padding  backdrop-blur-md backdrop-opacity-90 bg-[#f5f5f5]/80 saturate-100 backdrop-contrast-100"
            role="alert"
          >
            <div className="flex flex-col sm:flex-row justify-center items-center gap-4">
              <div className="py-1 ">
                <img
                  src="/dinobot-logo.svg"
                  alt="DinoBot Logo"
                  className="w-36 sm:w-48 mb-4"
                />
              </div>
              <div className="flex flex-col justify-center">
                <p className="text-xl font-bold text-dinoBotBlue mb-2 text-center sm:text-left">
                  {t('signup.step.send')}
                </p>
                <p className="text-sm">
                  {t('signup.step.send-text')}
                  {timer} {t('signup.step.send-text2')}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <button
                className={`bg-dinoBotBlue hover:bg-white hover:text-dinoBotBlue border border-dinoBotBlue text-white font-bold py-2 px-4 rounded-2xl transition-all duration-300 ${
                  canResend
                    ? ''
                    : 'opacity-50 cursor-not-allowed'
                }`}
                onClick={handleResendEmail}
                disabled={!canResend || isResendLoading}
              >
                {isResendLoading ? t('signup.step.resend') + '...' : t('signup.step.resend')}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className=" relative bg-white size-full flex flex-col gap-11 justify-evenly items-center  pb-10"
      style={{ backgroundImage: "url('/bg-with-icons.webp')" }}
    >
      <img
        src={'/auth-bg-icons.webp'}
        alt="oui active"
        width={908}
        height={465}
        className="absolute  md:block  sm:block top-0 left-1/2 -translate-x-1/2"
      />
      {/* <div className=' absolute top-0 left-0 w-full  h-[465px] bg-no-repeat object-contain bg-top' style={{ backgroundImage: "url('/auth-bg-icons.webp')" }}></div> */}
      <div className="mt-10">
        <img
          src={'/oui-active.svg'}
          alt="oui active"
          width={322}
          height={55}
        />
      </div>
      {!isRegistered ? (
        <div className="relative sm:w-3/4 md:w-[450px] ">
          <form
            onSubmit={handleSubmit}
            className="flex flex-col items-center gap-4 space-y-3 w-full "
          >
            <div className="w-full flex-1 rounded-lg border drop-shadow-md px-6 pb-4 pt-8 shadow-sm dark:bg-zinc-950 z-50 bg-clip-padding  backdrop-blur-md backdrop-opacity-90 bg-[#f5f5f5] saturate-100 backdrop-contrast-100">
              <h1 className="mb-3 text-3xl font-bold text-center text-slate-800 ">
                {t('signup.step.title')}
              </h1>
              {registrationType === 'both' && (
                <div className="w-full mt-4 flex justify-around h-10 gap-1 ">
                  <label
                    htmlFor="is-teacher-radio"
                    className="w-1/2 flex items-center  gap-2  p-2 bg-white border border-dinoBotDarkGray  rounded-lg flex-1 cursor-pointer"
                  >
                    <input
                      type="radio"
                      name="type"
                      value={'teacher'}
                      id="is-teacher-radio"
                      checked={formData.type === 'teacher'}
                      onChange={handleTypeChange}
                    />
                    <p className="text-sm text-nowrap font-medium text-dinoBotGray mt-0.5">
                      {t('signup.step.prof')}
                    </p>
                  </label>
                  <label
                    htmlFor="is-student-radio"
                    className="w-1/2 flex items-center gap-2 p-2 bg-white border border-dinoBotDarkGray  rounded-lg flex-1 cursor-pointer"
                  >
                    <input
                      type="radio"
                      name="type"
                      value={'student'}
                      id="is-student-radio"
                      checked={formData.type === 'student'}
                      onChange={handleTypeChange}
                    />
                    <p className="text-sm text-nowrap font-medium text-dinoBotGray mt-0.5">
                      {t('signup.step.student')}
                    </p>
                  </label>
                </div>
              )}
              {registrationType === 'student' && (
                <input
                  type="hidden"
                  name="type"
                  value="student"
                />
              )}
              {registrationType === 'teacher' && (
                <input
                  type="hidden"
                  name="type"
                  value="teacher"
                />
              )}
              <div className="w-full mt-3">
                <div>
                  <label
                    className="block text-lg font-medium text-dinoBotBlue "
                    htmlFor="email"
                  >
                    {t('signup.step.email.title')}
                    <span className="text-dinoBotRed font-semibold">
                      *
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[6px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                      id="email"
                      type="email"
                      name="email"
                      placeholder={t('signup.step.email.placeholder')}
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <label
                    className="block text-lg font-medium text-dinoBotBlue "
                    htmlFor="password"
                  >
                    {t('signup.step.password.title')}
                    <span className="text-dinoBotRed font-semibold">
                      *
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[6px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950 pr-10"
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      placeholder={t('signup.step.password.placeholder')}
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                      minLength={6}
                    />
                    <PasswordVisibility
                      passwordVisibility={showPassword}
                      showPassword={() => handleShowPassword(setShowPassword)(showPassword)}
                      hidePassword={() => handleShowPassword(setShowPassword)(showPassword)}
                    />
                  </div>
                </div>
                <div className="mt-3">
                  <label
                    className="block text-lg font-medium text-dinoBotBlue "
                    htmlFor="confirm-password"
                  >
                    {t('signup.step.confirm.title')}
                    <span className="text-dinoBotRed font-semibold">
                      *
                    </span>
                  </label>
                  <div className="relative">
                    <input
                      className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[6px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950 pr-10"
                      id="confirm-password"
                      type={showPassword2 ? 'text' : 'password'}
                      name="password2"
                      placeholder={t('signup.step.confirm.placeholder')}
                      value={formData.password2}
                      onChange={handleInputChange}
                      required
                      minLength={6}
                    />
                    <PasswordVisibility
                      passwordVisibility={showPassword2}
                      showPassword={() => handleShowPassword(setShowPassword2)(showPassword2)}
                      hidePassword={() => handleShowPassword(setShowPassword2)(showPassword2)}
                    />
                  </div>
                </div>
                <div className="flex w-full items-center space-x-2 my-3">
                  <input
                    type="checkbox"
                    id="terms"
                    className="data-[state=checked]:bg-dinoBotBlue text-white border-dinoBotBlue"
                    checked={acceptedTerms}
                    onChange={handleCheckChange}
                  />
                  <label className="text-xs" htmlFor="terms">
                    {t('signup.step.accepte')}
                    <Link
                      to="/cgu"
                      rel="noopener noreferrer"
                      target="_blank"
                      className=" ml-1 text-dinoBotBlue"
                    >
                      {t('signup.step.terms')}
                    </Link>
                  </label>
                </div>
              </div>
              <div className="w-full flex justify-center">
                <LoginButton acceptedTerms={acceptedTerms} />
              </div>
              <Link
                to="/login"
                className="flex flex-row gap-1 justify-center items-center text-xs  text-dinoBotGray w-full mt-3"
              >
                <div>{t('signup.step.have')}</div>
                <div className="text-dinoBotVibrantBlue underline">
                  {t('signup.step.connexion')}
                </div>
              </Link>
            </div>
          </form>
          <img
            src={'/dinobot-all.svg'}
            alt="dinobot"
            width={260}
            height={280}
            className="absolute bottom-10 left-3/4 "
          />
          <img
            src={'/dinobot-tv.svg'}
            alt="dinobot"
            width={175}
            height={175}
            className="absolute bottom-20 right-[85%] "
          />
        </div>
      ) : null}
    </div>
  );
};

export default SignupForm;
