import { MemoizedReactMarkdown } from '@dinobot/components-ui'
import { Button } from '@dinobot/components-ui'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle
} from '@dinobot/components-ui'
import { Minus, Plus } from 'lucide-react'
import React from 'react'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypemathjax from 'rehype-mathjax'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { cn } from '@dinobot/utils'
import { QuestionCardType } from './question-content'

type QuestionCardProps = {
    qst?: QuestionCardType
    listQst: QuestionCardType[]
    onAddClick: (qst: QuestionCardType) => void
    onRemoveClick: (qst: QuestionCardType) => void
}

const QuestionCard = ({
    qst,
    listQst,
    onAddClick,
    onRemoveClick
}: QuestionCardProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/dialog/db')
    const dir = getLangDir(i18n.language)
    return (
        <Card
            className="border border-dinoBotBlue/80 rounded-md w-full"
            style={{ direction: dir }}
        >
            <CardHeader className="p-2">
                <CardTitle className="flex items-center justify-between font-normal gap-1">
                    <span>{qst?.knowledge}</span>
                    {(listQst.find(l => l.id === qst?.id)?.questionNbr ?? 0) >
                    0 ? (
                        <div className="flex h-fit items-center border border-dinoBotGray rounded-lg">
                            <Button
                                onClick={() => onRemoveClick(qst!)}
                                variant={'ghost'}
                                className=" p-0 flex justify-center items-center size-7"
                            >
                                <Minus />
                            </Button>
                            <p>
                                {
                                    listQst.find(l => l.id === qst?.id)
                                        ?.questionNbr
                                }
                            </p>
                            <Button
                                onClick={() => onAddClick(qst!)}
                                variant={'ghost'}
                                className=" p-0 flex justify-center items-center size-7"
                            >
                                <Plus />
                            </Button>
                        </div>
                    ) : (
                        <Button
                            variant="link"
                            className=" text-dinoBotBlue hover:text-dinoBotVibrantBlue px-0 hover:no-underline"
                            onClick={() => onAddClick(qst!)}
                        >
                            <Plus />
                            {t('add')}
                        </Button>
                    )}
                </CardTitle>
                <CardDescription></CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col gap-2 p-2">
                <div className="rounded-md bg-dinoBotBlue/20 w-full px-4 py-2">
                    <MemoizedReactMarkdown
                        remarkPlugins={[remarkGfm, remarkMath]}
                        rehypePlugins={[rehypeRaw, rehypemathjax]}
                        components={{
                            p({ children }) {
                                return (
                                    <p className="overflow-x-hidden">
                                        {children}
                                    </p>
                                )
                            }
                        }}
                    >
                        {qst?.question}
                    </MemoizedReactMarkdown>
                </div>
                <div className="flex flex-col">
                    <h5 className="text-dinoBotBlackBlue">{t('answer')}</h5>
                    <div className="px-3 py-1 rounded-md border border-dinoBotVibrantBlue bg-background text-wrap">
                        <MemoizedReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeRaw, rehypemathjax]}
                            components={{
                                p({ children }) {
                                    return (
                                        <p
                                            className={cn(
                                                'overflow-x-hidden text-ellipsis'
                                            )}
                                        >
                                            {children}
                                        </p>
                                    )
                                }
                            }}
                        >
                            {qst?.solution}
                        </MemoizedReactMarkdown>
                    </div>
                </div>
                {/* <div className="flex flex-col">
					<h6 className='text-dinoBotBlackBlue'>Compétences</h6>
					<p className='px-3 py-1 rounded-md border border-dinoBotVibrantBlue bg-background text-sm'>Manipuler les nombres réels</p>
				</div> */}
            </CardContent>
        </Card>
    )
}

export default QuestionCard
