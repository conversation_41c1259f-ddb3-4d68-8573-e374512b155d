import React, { Ref } from 'react'
import { DesmosUi } from '@dinobot/components-ui'
import { ControlExercisePartialWithRelations } from '@dinobot/prisma'
import { Domain } from '@dinobot/prisma'
import { Level } from '@dinobot/prisma'
import { DesmosSchema } from '@dinobot/utils'
import { z } from 'zod'
import QuestionViewer from './question-viewer'
import { cn } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { getLangProps } from '@dinobot/utils'
import { MediasPreviewer, Media } from '@dinobot/components-ui'

type ControleViewerProps = {
    exos: ControlExercisePartialWithRelations[]
    domain: Domain
    level: Level
    name: string | undefined
    font: string
    fontSize: number
    ref: Ref<HTMLDivElement>
}

const ControleViewer = ({
    exos,
    domain,
    level,
    name,
    font,
    fontSize,
    ref
}: ControleViewerProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/preview')
    const dir = getLangDir(i18n.language)

    return (
        <div
            className="w-full min-h-full h-auto  bg-white border grow px-6 pb-8 leading-7"
            ref={ref}
            dir={dir}
        >
            <div className="w-full flex justify-center items-center py-6  flex-col ">
                <div className="font-normal text-lg underline text-dinoBotDarkGray">
                    {name ? name : 'Nom de la fiche'}
                </div>
                <div
                    className="font-normal text-sm  text-dinoBotDarkGray"
                    dir={dir}
                >{`${getLangProps({ obj: domain, base: 'name', lang: i18n.language }) ?? t('domain')} ${getLangProps({ obj: level, base: 'name', lang: i18n.language }) ?? t('level')}`}</div>
            </div>
            <div
                className={cn('flex flex-col gap-4')}
                style={{ fontFamily: font, fontSize: fontSize }}
            >
                {exos &&
                    exos[0] &&
                    (exos[0]?.questions || []).length > 0 &&
                    exos.map((exo, index) => (
                        <div key={index}>
                            <div>
                                {t('exo')} {index + 1}
                            </div>
                            {exo.hasStatment && (
                                <div className="flex flex-row gap-2 p-2">
                                    <QuestionViewer value={exo.statement!} />
                                    <MediasPreviewer
                                        className="h-24 w-44"
                                        mediaList={
                                            exo?.medias?.filter(
                                                media => media.type === 'global'
                                            ) as Media[]
                                        }
                                        fileTypeKey="fileType"
                                        fileUrlKey="fileUrl"
                                    />
                                </div>
                            )}
                            {exo.medias && exo?.medias.length > 0 && (
                                <MediasPreviewer
                                    className="h-24 w-44"
                                    mediaList={
                                        exo?.medias.filter(
                                            media => media.type === 'statement'
                                        ) as Media[]
                                    }
                                    fileTypeKey="fileType"
                                    fileUrlKey="fileUrl"
                                />
                            )}
                            {exo?.questions && exo?.questions?.length > 0 ? (
                                <div className="flex flex-col gap-2">
                                    {exo.questions?.map((qst, index) => (
                                        <div key={index}>
                                            <div className="flex gap-1 items-start">
                                                <div className="pt-0.5">
                                                    {index + 1}.
                                                </div>
                                                <QuestionViewer
                                                    value={qst.content!}
                                                />
                                                <MediasPreviewer
                                                    className="h-36 w-44 flex flex-wrap p-2"
                                                    mediaList={
                                                        qst?.medias?.filter(
                                                            media =>
                                                                media.type ===
                                                                'question'
                                                        ) as Media[]
                                                    }
                                                    fileTypeKey="fileType"
                                                    fileUrlKey="fileUrl"
                                                />
                                            </div>

                                            {qst.desmosCode &&
                                            (
                                                qst.desmosCode as z.infer<
                                                    typeof DesmosSchema
                                                >
                                            ).expressions ? (
                                                <DesmosUi
                                                    data={
                                                        qst.desmosCode as z.infer<
                                                            typeof DesmosSchema
                                                        >
                                                    }
                                                    className="w-96 h-80"
                                                />
                                            ) : (
                                                ''
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ) : null}
                        </div>
                    ))}
            </div>
        </div>
    )
}

export default ControleViewer
