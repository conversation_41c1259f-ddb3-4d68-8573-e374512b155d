// src/pages/teacher/my-classes/components/ConfirmDialog.tsx
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@dinobot/components-ui'
import { Checkbox, Label } from '@dinobot/components-ui'
import { DialogState, ControlPartial } from '../../about-tab.types'

interface ConfirmDialogProps {
  deleteDialogOpen: boolean
  dispatch: (action: Partial<DialogState>) => void
  onConfirm: () => void
  evaluation: ControlPartial
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  deleteDialogOpen,
  dispatch,
  onConfirm,
  evaluation
}) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  const [neverAsk, setNeverAsk] = useState(false)

  const handleConfirm = () => {
    // Save the "never ask" preference
    localStorage.setItem('never-ask-delete-dialog', neverAsk ? 'true' : 'false')
    
    onConfirm()
    dispatch({ deleteDialogOpen: false })
    console.log('Suppression effectuée')
  }

  return (
    <AlertDialog open={deleteDialogOpen}>
      <AlertDialogContent className="border-1 border-dinoBotBlackBlue max-w-lg">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex justify-center p-2">
            {t('confirm_dialog.title', { ns: 'teacher/my-class/apropo' })}
          </AlertDialogTitle>
          <AlertDialogDescription className="py-6">
            {t('confirm_dialog.content', { ns: 'teacher/my-class/apropo' })} (1) {' '}
            {t('confirm_dialog.content_next', { ns: 'teacher/my-class/apropo' })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <div className="w-full mt-14">
            <div className="flex justify-start items-end h-fit py-6 text-dinoBotGray">
              <Checkbox
                id="never-ask-delete-dialog"
                name="never-ask-delete-dialog"
                onCheckedChange={value => setNeverAsk(value === true)}
                checked={neverAsk}
              />
              <Label
                className="px-2 text-sm"
                htmlFor="never-ask-delete-dialog"
              >
                {t('confirm_dialog.never_ask', { ns: 'teacher/my-class/apropo' })}
              </Label>
            </div>
            <div className="flex justify-between w-full">
              <AlertDialogCancel
                className="text-dinoBotWhite bg-dinoBotGray"
                onClick={() => dispatch({ deleteDialogOpen: false })}
              >
                {t('confirm_dialog.cancel', { ns: 'teacher/my-class/apropo' })}
              </AlertDialogCancel>
              <AlertDialogAction
                className="text-dinoBotWhite bg-dinoBotRed"
                onClick={handleConfirm}
              >
                {t('confirm_dialog.confirm', { ns: 'teacher/my-class/apropo' })}
              </AlertDialogAction>
            </div>
          </div>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default ConfirmDialog