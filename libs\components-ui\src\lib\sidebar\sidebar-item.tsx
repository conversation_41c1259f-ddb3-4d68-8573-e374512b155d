//Hooks
import * as React from 'react';
import { useSidebar, useLocalStorage, useIsTouchDevice, useFilesSidebar } from '@dinobot/hooks';

//Motion
import { motion } from 'framer-motion';

//UI
import {
  Button,
  buttonVariants,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@dinobot/components-ui';

//Icons
import { IconCheck, IconEdit, IconMessage } from '@dinobot/components-ui';
import {
  Atom,
  BookOpen,
  Earth,
  FlaskConical,
  Hourglass,
  Leaf,
  Pi,
  Users, School, Pin, PinOff
} from 'lucide-react';

//Types
import { ChatWithoutMessages } from '@dinobot/utils'

//Utils
import { cn } from '@dinobot/utils';

//Cookies
import Cookies from 'js-cookie'


//API
import { useExamsStore } from '@dinobot/stores';
// TODO api link
// import { getExercise } from '@/lib/exams/actions';
// import { pinChat, unpinChat, updateChatTitle } from '@/app/[locale]/actions';

//Stores
import { useTopicStore, useMobileSidebarStore, useChatStore } from '@dinobot/stores';


//Math-Ajax
import { MathJax, MathJaxContext } from 'better-react-mathjax';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface SidebarItemProps {
  index: number;
  chat: ChatWithoutMessages;
  children: React.ReactNode;
}

export function SidebarItem({ index, chat, children }: SidebarItemProps) {
  const {t} = useTranslation(['app.headers'],{keyPrefix: 'history'});

  const pathname = useLocation().pathname
  

  const { openSidebar, closeSidebar } = useSidebar();

  const { closeFilesSidebar } = useFilesSidebar();

  const { setRefreshChatsList, setRefreshChatList } = useChatStore();

  const setTopic = useTopicStore((state) => state.setTopic);

  const { closeChatMobileSidebar } = useMobileSidebarStore();

  const isTouchDevice = useIsTouchDevice();

  const isActive = pathname === chat.path;
  const [newChatId, setNewChatId] = useLocalStorage('newChatId', null);
  const shouldAnimate = index === 0 && isActive && newChatId;

  const { setExercise } = useExamsStore();

  const [currentEditableItem, setCurrentEditableItem] = React.useState(null);
  const [temporaryTitle, setTemporaryTitle] = React.useState(chat.title);

  async function handleChangeTitle(id: string, newTitle: string) {
    // TODO
    // await updateChatTitle(id, newTitle);
    setRefreshChatList(true);
  }

  async function handlePinToggle() {
    // TODO
    // if (chat?.pinned) await unpinChat(chat.id);
    // else pinChat(chat.id);
    setRefreshChatsList(true);
  }

  const getCurrentExercise = async (id: string) => {
    // TODO
    // const currentExercise = await getExercise(id);
    // setExercise(currentExercise);
    //logger.debug('current exercise: ' + JSON.stringify(exercise, null, 2))
  };

  if (!chat?.id) return null;

  function topicIconRenderer(topic: string) {
    switch (topic) {
      case 'Mathématiques':
        return (
          <div title="Conversation en relation avec les mathématiques">
            <Pi className="text-dinoBotRed mr-3" />
          </div>
        );
      case 'Géographie':
        return (
          <div title="Conversation en relation avec les mathématiques">
            <Earth className="text-dinoBotPurple mr-3" />
          </div>
        );
      case 'Français':
        return (
          <div title="Conversation en relation avec les mathématiques">
            <BookOpen className="text-dinoBotSky mr-3" />
          </div>
        );
      case 'Physique-Chimie':
        return (
          <div title="Conversation en relation avec la physique">
            <Atom className="text-dinoBotBlue mr-3" />
          </div>
        );
      case 'Chimie':
        return (
          <div title="Conversation en relation avec la chimie">
            <FlaskConical className="text-dinoBotCyan mr-3" />
          </div>
        );
      case 'SVT':
        return (
          <div title="Conversation en relation avec les sciences de la vie et de la terre">
            <Leaf className="text-dinoBotGreen mr-3" />
          </div>
        );
      case 'EMC':
        return (
          <div title="Conversation en relation avec une autre matière">
            <Users className="text-dinoBotRoseBonbon mr-3" />
          </div>
        );
      case 'Histoire':
        return (
          <div title="Conversation en relation avec une autre matière">
            <Hourglass className="text-dinoBotYellow mr-3" />
          </div>
        );
      default:
        return <IconMessage className="mr-2" />;
    }
  }

  return (
    <motion.div
      className="relative h-8"
      variants={{
        initial: {
          height: 0,
          opacity: 0,
        },
        animate: {
          height: 'auto',
          opacity: 1,
        },
      }}
      initial={shouldAnimate ? 'initial' : undefined}
      animate={shouldAnimate ? 'animate' : undefined}
      transition={{
        duration: 0.25,
        ease: 'easeIn',
      }}
    >
      <div className=" absolute left-2 top-1 flex size-6 items-center justify-center">
        {/*         {chat.sharePath ? (
          <Tooltip delayDuration={1000}>
            <TooltipTrigger
              tabIndex={-1}
              className="focus:bg-muted focus:ring-1 focus:ring-ring"
            >
              <IconUsers className="mr-2" />
            </TooltipTrigger>
            <TooltipContent className='bg-dinoBotBlue'>Ceci est un chat partagé.</TooltipContent>
          </Tooltip>
        ) : ( */}
        {topicIconRenderer(chat.topic)}

        {/*         )} */}
      </div>
      <Link
        to={`/${chat.exerciseId ? 'exam' : ''}/${chat.path}`}
        onClick={() => {
          Cookies.set('topic', chat.topic);
          setTopic(chat.topic);
          if (chat.exerciseId) {
            getCurrentExercise(chat.exerciseId);
            Cookies.set('feature', 'Exam');
            Cookies.set('mode', 'GPT 4-o');
            closeSidebar();
            closeFilesSidebar();
            if (isTouchDevice) {
              closeChatMobileSidebar();
            }
          }
          if (!chat.exerciseId) {
            setExercise(null);
            Cookies.set('feature', 'Chat');
            openSidebar();
            if (isTouchDevice) {
              closeChatMobileSidebar();
            }
          }
        }}
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'group w-full px-8 transition-colors hover:bg-zinc-200/40 dark:hover:bg-zinc-300/10',
          isActive && 'bg-zinc-200 pr-16 font-semibold dark:bg-zinc-800',
        )}
      >
        <div
          className={`relative max-h-5 flex-1 select-none overflow-hidden text-ellipsis break-all ${isActive ? 'mr-6' : ''}`}
          title={chat.title}
        >
          <span className="whitespace-nowrap">
            {shouldAnimate ? (
              chat.title.split('').map((character: string, index: number) => (
                <motion.span
                  key={index}
                  variants={{
                    initial: {
                      opacity: 0,
                      x: -100,
                    },
                    animate: {
                      opacity: 1,
                      x: 0,
                    },
                  }}
                  initial={shouldAnimate ? 'initial' : undefined}
                  animate={shouldAnimate ? 'animate' : undefined}
                  transition={{
                    duration: 0.25,
                    ease: 'easeIn',
                    delay: index * 0.05,
                    staggerChildren: 0.05,
                  }}
                  onAnimationComplete={() => {
                    if (index === chat.title.length - 1) {
                      setNewChatId(null);
                    }
                  }}
                >
                  {character}
                </motion.span>
              ))
            ) : (
              <div
                /*                 onMouseEnter={() => {
                  if (isActive) {
                    setCurrentEditableItem(chat.id)
                  }
                }}  */
                /*                 onMouseLeave={() => {
                  if (isActive) {
                    setCurrentEditableItem(null)
                    handleChangeTitle(chat.id, temporaryTitle)
                  }
                }}  */
                className="flex flex-row"
              >
                {chat.exerciseId && (
                  <div
                    title="Tentative d'examen"
                    className="size-6 text-dinoBotBlue mr-2"
                  >
                    {' '}
                    <School />
                  </div>
                )}
                {currentEditableItem === chat.id ? (
                  <input
                    className="px-1"
                    value={temporaryTitle}
                    onChange={(e) => setTemporaryTitle(e.target.value)}
                  />
                ) : (
                  <MathJaxContext>
                    <MathJax dynamic>
                      <span>{chat.title}</span>
                    </MathJax>
                  </MathJaxContext>
                )}
              </div>
            )}
          </span>
        </div>
      </Link>
      {isActive ? (
        <div className="absolute right-8 top-1">
          {currentEditableItem === chat.id ? (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  className="size-7 p-0 hover:bg-background"
                  onClick={() => {
                    setCurrentEditableItem(null);
                    handleChangeTitle(chat.id, temporaryTitle);
                  }}
                >
                  <IconCheck />
                  <span className="sr-only">{t('save')}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent className="bg-dinoBotBlue">
                {t('save')}
              </TooltipContent>
            </Tooltip>
          ) : (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    className="size-7 p-0 hover:bg-background"
                    onClick={() => handlePinToggle()}
                  >
                    {chat.pinned ? (
                      <PinOff strokeWidth={1.4} size={16} />
                    ) : (
                      <Pin strokeWidth={1.4} size={16} />
                    )}
                    <span className="sr-only">
                      {chat.pinned ? t('no-epingle-chat') : t('epingle-chat')}
                    </span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                  {chat.pinned ? t('no-epingle-chat') : t('epingle-chat')}
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    className="size-7 p-0 hover:bg-background"
                    onClick={() => setCurrentEditableItem(chat.id)}
                  >
                    <IconEdit />
                    <span className="sr-only">{t('update')}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                  {t('update')}
                </TooltipContent>
              </Tooltip>
            </>
          )}
        </div>
      ) : null}
      {isActive ? (
        <div className="absolute right-2 top-1">{children}</div>
      ) : null}
    </motion.div>
  );
}
