{"fr": "French", "en": "English", "ar": "Arabic", "errors": {"400": "Bad Request - Invalid data", "401": "Session expired. Please log in again.", "403": "Access forbidden - Insufficient permissions", "404": "Resource not found", "405": "Method not allowed", "408": "Request timeout exceeded", "409": "Conflict - Resource already exists", "410": "Resource permanently deleted", "422": "Unprocessable data - Validation failed", "429": "Too many requests - Please wait", "500": "Internal server error", "501": "Feature not implemented", "502": "Bad gateway", "503": "Service temporarily unavailable", "504": "Gateway timeout exceeded", "network": "Network connection problem", "timeout": "Connection timeout exceeded", "connection": "Network error - Check your connection", "request": "Connection error", "unknown": "An error occurred"}}