import { Button } from '@dinobot/components-ui'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    Di<PERSON>Header,
    DialogTitle,
    DialogTrigger
} from '@dinobot/components-ui'
import { Input } from '@dinobot/components-ui'
import { Label } from '@dinobot/components-ui'
import { CircleCheckBig, FileUp, Info, Loader2, Upload } from 'lucide-react'
import React, { useId, useReducer } from 'react'
import { toast } from 'sonner'
import { selectEvaluationParamsStore } from '../../../store/evaluation-params.store'
import {
    JsonArray,
    JsonObject,
    JsonValue
} from '@prisma/client/runtime/library'
import { useTranslation } from 'react-i18next'
import { Switch } from '@dinobot/components-ui'
import { cn } from '@dinobot/utils'
import DialogExoQuestionsFromFileSelect from './dialog-exo-questions-from-file-select'
import { useGenerateControleByFile } from '../../../hooks/useGenerateControleByFile'

type DialogQuestionsFromFileProps = {
    exoIndex: number
    quesNum: number
}

const DialogExoQuestionsFromFile = ({
    exoIndex,
    quesNum
}: DialogQuestionsFromFileProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/file')
    const evalProps = selectEvaluationParamsStore.use.evalProps()
    const switchId = useId()
    const initState = {
        open: false,
        openSelect: false,
        data: {} as { exo?: File; solution?: File },
        isCourse: false,
        questions: [] as
            | {
                  content: string
                  desmosCode:
                      | string
                      | number
                      | boolean
                      | JsonObject
                      | JsonArray
                      | null
                  solution: string
                  description: string
              }[]
            | undefined
    }
    const [{ open, openSelect, data, isCourse, questions }, dispatch] =
        useReducer(
            (state: typeof initState, change: Partial<typeof initState>) => ({
                ...state,
                ...change
            }),
            initState
        )

    const generateControleByFileMutation = useGenerateControleByFile()
    
    const handleFormChange = async (
        field: keyof typeof data,
        value: File | number | string
    ) => {
        dispatch({ data: { ...data, [field]: value } })
    }

    const handleAddExo = async () => {
        try {
            const formFiles = new FormData()
            formFiles.append('file', data.exo!)
            if (data.solution) {
                formFiles.append('solutionFile', data.solution)
            }
            
            const ques = await generateControleByFileMutation.mutateAsync({
                formFiles,
                isCourse,
                domainName: evalProps?.domainName ?? '',
                levelName: evalProps?.assignedClass?.level?.name ?? '',
                documentType: isCourse ? 'cours' : 'exercice'
            })
            
            if (!ques || ques.length === 0) {
                throw new Error('No questions generated')
            }
            
            const questions = ques[0]?.map(que => ({
                content: que?.content,
                desmosCode: que.desmosCode as JsonValue,
                solution: que.solution,
                description: que.description
            }))
            
            dispatch({ questions, openSelect: true })
        } catch (error) {
            console.error('Error generating questions:', error)
            toast.error(t('error'))
        } finally {
            dispatch({ open: false })
        }
    }

    return (
        <>
            <DialogExoQuestionsFromFileSelect
                exoIndex={exoIndex}
                quesNum={quesNum}
                onOpenChange={open => dispatch({ openSelect: open })}
                open={openSelect}
                questions={questions!}
            />
            <Dialog open={open} onOpenChange={open => dispatch({ open })}>
                <DialogTrigger className="flex flex-col items-center justify-center hover:no-underline gap-1 h-fit">
                    <div className="rounded-full flex justify-center items-center border p-1 border-dinoBotVibrantBlue/30 bg-dinoBotWhite/80">
                        <FileUp className="size-7" />
                    </div>
                    <span>{t('import')}</span>
                </DialogTrigger>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle className="flex gap-2 text-lg items-center">
                            {t('import_exo')}
                            <Info />
                        </DialogTitle>
                        <DialogDescription></DialogDescription>
                    </DialogHeader>
                    <div className="w-full mt-4 flex flex-col gap-2">
                        <div className="flex gap-4 items-center">
                            <Label htmlFor={switchId}>{t('exo')}</Label>
                            <Switch
                                id={switchId}
                                className="data-[state=checked]:bg-dinoBotBlue data-[state=unchecked]:bg-dinoBotLightBlue"
                                checked={isCourse}
                                onCheckedChange={isCourse =>
                                    dispatch({ isCourse: isCourse })
                                }
                            />
                            <Label htmlFor={switchId}>{t('course')}</Label>
                        </div>
                        <div>
                            <h3>
                                {t('question_file', {
                                    iscours: isCourse ? 'cours' : 'exercice'
                                })}
                            </h3>
                            <Label
                                htmlFor="exo-file"
                                className={`gap-2 bg-dinoBotGray/50 text-dinoBotDarkGray hover:bg-dinoBotGray/60 flex items-center px-4 w-[14.5rem] h-8  rounded-md cursor-pointer transition-colors duration-200 ${data?.exo ? 'bg-dinoBotGreen/20' : 'bg-dinoBotGray/50'}`}
                            >
                                {data?.exo ? <CircleCheckBig /> : <Upload />}
                                <p className="gap-2">
                                    {data?.exo ? t('replace') : t('upload')}
                                    <span className="text-dinoBotRed">*</span>
                                </p>
                            </Label>
                            <Input
                                className="hidden"
                                type="file"
                                name="exo-file"
                                id="exo-file"
                                accept="application/pdf"
                                onChange={e => {
                                    if (e.target.files)
                                        handleFormChange(
                                            'exo',
                                            e.target.files[0]
                                        )
                                }}
                            />
                            <p className="text-dinoBotGray font-light text-sm">
                                <span className="text-dinoBotRed">*</span>
                                {t('file_max')}
                            </p>
                        </div>
                        <div className={cn({ hidden: isCourse })}>
                            <h3>{t('solution_file')}</h3>
                            <Label
                                htmlFor="sol-file"
                                className={`gap-2 bg-dinoBotGray/50 text-dinoBotDarkGray hover:bg-dinoBotGray/60 flex items-center px-4 w-[14.5rem] h-8  rounded-md cursor-pointer transition-colors duration-200 ${data?.solution ? 'bg-dinoBotGreen/20' : 'bg-dinoBotGray/50'}`}
                            >
                                {data?.solution ? (
                                    <CircleCheckBig />
                                ) : (
                                    <Upload />
                                )}
                                <p className="gap-2">
                                    {data?.solution
                                        ? t('replace')
                                        : t('upload')}
                                    <span className="text-dinoBotRed">*</span>
                                </p>
                            </Label>
                            <Input
                                className="hidden"
                                type="file"
                                name="sol-file"
                                id="sol-file"
                                accept="application/pdf"
                                onChange={e => {
                                    if (e.target.files)
                                        handleFormChange(
                                            'solution',
                                            e.target.files[0]
                                        )
                                }}
                            />
                            <p className="text-dinoBotGray font-light text-sm">
                                <span className="text-dinoBotRed">*</span>
                                {t('file_max')}
                            </p>
                        </div>
                    </div>
                    <DialogFooter className="w-full flex sm:justify-center items-center mt-4">
                        <Button
                            onClick={handleAddExo}
                            disabled={generateControleByFileMutation.isPending}
                            className="bg-dinoBotBlue text-white hover:bg-dinoBotBlue/80 hover:text-white"
                        >
                            {generateControleByFileMutation.isPending ? (
                                <>
                                    <Loader2 className="mr-2 size-4 animate-spin" />
                                    {t('loading')}
                                </>
                            ) : (
                                t('add')
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}

export default DialogExoQuestionsFromFile
