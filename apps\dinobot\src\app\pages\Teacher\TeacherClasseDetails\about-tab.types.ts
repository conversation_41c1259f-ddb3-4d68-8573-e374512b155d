import { StudentAnswerSchema } from "@dinobot/prisma"

// src/pages/teacher/my-classes/about-tab.types.ts
export interface ClassWithRelations {
  id: string
  name: string
  classColor?: string
  scholarYear?: string
  domain?: Domain
  level?: Level
  students: Student[]
  createdAt: Date
  updatedAt: Date
  mainTeacher: Teacher
  controls: Evaluation[]
}

interface Evaluation {
  id: string;
  name: string;
  description: string | null;
  hasSolution: boolean;
  isTimed: boolean | null;
  scoringType: 'NUMERIC' | string;
  status: 'IN_PROGRESS' | string;
  domainName: string;
  levelName: string | null;
  fontName: string;
  fontSize: number;
  type: 'FORMATIVE' | string;
  numericMaxScore: number | null;
  letterRange: string | null;
  criteriaChoices: string | null;
  authorId: string;
  assignedClassId: string;
  themeId: string;
  evaluationPromptId: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Domain {
  id: number
  name: string
}

export interface Level {
  id: number
  name: string
}

export interface Student {
    id: string,
    firstName: string,
    lastName: string,
    email: string,
    classMedian: [],
    totalMedian: [],
    invitationSent: false,
    userId: string,
    classId: string
}

export interface Theme {
  id: string
  name: string
  controls: ControlPartial[]
  _count: { controls: number }
}

export interface ControlPartial {
  id: string
  name: string
  description?: string
  status: 'DRAFT' | 'ASSIGNED'
  fontName: string
  fontSize: number
  createdAt: Date
  updatedAt: Date
  assignedClass?: ClassWithRelations
  exercises?: ExercisePartial[]
  submissions?: SubmissionPartial[]
}

export interface ExercisePartial {
  id: string
  title: string
  type: string
}

export interface SubmissionPartial {
  id: string
  status: string
}

export interface DialogState {
  deleteDialogOpen: boolean
  evaluationAlertOpen: boolean
}

export interface EvaluationCardProps {
  evaluation?: ControlPartial
  isEvalOrTask?: boolean
}

export interface RadioEvaluationCardProps {
  label: string
  evaluation: ControlPartial
}

export interface ApropoStoreState {
  classes: ClassWithRelations | null
  themes: Theme[]
  controls: ControlPartial[]
  checkedEvaluations: string[]
  themeControls: Record<string, ControlPartial[]>
  selectedThemeId: string | null
}

export interface ApropoStoreActions {
  setClass: (classes: ClassWithRelations) => void
  setThemes: (themes: Theme[]) => void
  setControls: (controls: ControlPartial[]) => void
  setCheckedEvaluations: (checkedEvaluations: string[]) => void
  setThemeControls: (themeId: string, controls: ControlPartial[]) => void
  setSelectedThemeId: (themeId: string | null) => void
}
interface UserBase {
  id: string;
  email: string;
  password: string;
  salt: string;
  type: string;
  status: string;
  firstName: string;
  lastName: string;
  gender: string;
  birthDate: string | null;
  level: string;
  phoneNumber: string | null;
  emailVerified: boolean;
  verifyKey: string | null;
  verifyKeyDate: string | null;
  resetKey: string | null;
  resetKeyDate: string | null;
  deletionRequestedAt: string | null;
  scheduledDeletionAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface Teacher extends UserBase {
  isAdmin: boolean;
  levelId: number;
  maxDomains: number;
  maxLevelDomains: number;
  hasSelectLevelDomain: boolean;
}
