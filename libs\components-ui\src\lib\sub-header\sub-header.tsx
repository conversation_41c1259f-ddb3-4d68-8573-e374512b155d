//Hooks
import * as React from 'react'

//components
import { SubFeatureSwitcher } from './sub-feature-switcher'

import Cookies from 'js-cookie'
import { Session } from '@dinobot/utils'
//TODO ---------------
// import {
//     getFeatureFlags,
//     getFeaturesFlagsByNames
// } from '@/lib/features/actions'
// import { FeatureFlagName } from '@prisma/client'

export function SubHeader() {
    const session = (JSON.parse(Cookies.get('session') || '{}')) as Session

    // const featureFlags = await getFeaturesFlagsByNames([
    //     FeatureFlagName.STUDENT_EXERCISE_MODE,
    //     FeatureFlagName.STUDENT_EVALUATION_MODE,
    //     FeatureFlagName.STUDENT_EXAM_MODE,
    //     FeatureFlagName.STUDENT_CHAT_MODE
    // ])

    // if (!Array.isArray(featureFlags)) {
    //     return null
    // }

    // const exerciseModeEnabled =
    //     featureFlags.find(
    //         flag => flag.featureName === FeatureFlagName.STUDENT_EXERCISE_MODE
    //     )?.isEnabled || false

    // const evaluationModeEnabled =
    //     featureFlags.find(
    //         flag => flag.featureName === FeatureFlagName.STUDENT_EVALUATION_MODE
    //     )?.isEnabled || false

    // const examModeEnabled =
    //     featureFlags.find(
    //         flag => flag.featureName === FeatureFlagName.STUDENT_EXAM_MODE
    //     )?.isEnabled || false

    // const chatModeEnabled =
    //     featureFlags.find(
    //         flag => flag.featureName === FeatureFlagName.STUDENT_CHAT_MODE
    //     )?.isEnabled || false

    const avatarModeEnabled = import.meta.env.APP_AVATAR_MODE === 'true' || false

    return (
        <nav className="sticky top-0 z-10 flex flex-col lg:flex-row items-center w-full  gap-2 py-2 lg:py-2  px-4 border-b shrink-0 bg-gradient-to-b from-background/10 via-background/50 to-background/80 backdrop-blur-xl  shadow-lg shadow-dinoBotCyan/15">
            <div className="flex flex-row justify-between">
                <SubFeatureSwitcher
                    disabled={!session?.user}
                    // TODO DEcoment
                    // exerciseModeEnabled={exerciseModeEnabled}
                    // evaluationModeEnabled={evaluationModeEnabled}
                    // examModeEnabled={examModeEnabled}
                    // chatModeEnabled={chatModeEnabled}
                    exerciseModeEnabled={true}
                    evaluationModeEnabled={true}   
                    examModeEnabled={true}
                    chatModeEnabled={true}
                />
            </div>
        </nav>
    )
}
