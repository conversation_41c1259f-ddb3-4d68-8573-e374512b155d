//TODO - SERVER 

import * as React from 'react'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from '@dinobot/components-ui'
import { useCookies } from 'next-client-cookies'
import { Brain, BrainCircuit, BrainCog, Zap } from 'lucide-react'

export const SelectMode = () => {
    const cookies = useCookies()

    const mode = cookies.get('mode')

    React.useEffect(() => {
        //console.log("The actual mode stored in the cookies is " + mode)
        if (!mode) {
            cookies.set('mode', 'GPT 4-o')
        }
    }, [mode, cookies])

    const handleModeChange = (value: any) => {
        //console.log("La matière choisie est : " + value)
        cookies.set('mode', value)
        //window.location.reload()
    }

    return (
        <Select
            value={mode}
            onValueChange={handleModeChange}
            //disabled={feature === "Exam"}
        >
            <SelectTrigger
                className={`font-montserrat font-semibold w-[38vw] md:w-[18vw] flex flex-row justify-center items-center sm:text-base rounded-2xl 
                        ${mode === 'GPT 3.5 Turbo' ? 'text-dinoBotYellow border-dinoBotYellow hover:text-white hover:bg-dinoBotYellow' : ''} 
                        ${mode === 'GPT 4' ? 'text-dinoBotVividOrange border-dinoBotVividOrange hover:text-white hover:bg-dinoBotVividOrange' : ''} 
                        ${mode === 'GPT 4 Turbo' ? 'text-dinoBotPurple border-dinoBotPurple hover:text-white hover:bg-dinoBotPurple' : ''} 
                        ${mode === 'GPT 4-o' ? 'text-dinoBotRoseBonbon border-dinoBotRoseBonbon hover:bg-dinoBotRoseBonbon hover:text-white' : ''}
                         bg-white border animate-fade-in-right transition-all duration-300`}
            >
                <SelectValue placeholder="Sélectionnez un mode" />
            </SelectTrigger>
            <SelectContent className="rounded-2xl">
                <SelectGroup>
                    <SelectLabel>Modes</SelectLabel>
                    <SelectItem
                        className="font-montserrat font-semibold text-dinoBotYellow  focus:bg-dinoBotYellow rounded-2xl focus:text-white transition-all duration-300"
                        value="GPT 3.5 Turbo"
                    >
                        <div className="flex flex-row">
                            <Zap className="mr-2" />
                            GPT 3.5 Turbo
                        </div>
                    </SelectItem>
                    <SelectItem
                        className="font-montserrat font-semibold text-dinoBotVividOrange  focus:bg-dinoBotVividOrange rounded-2xl focus:text-white transition-all duration-300"
                        value="GPT 4"
                    >
                        <div className="flex flex-row">
                            <BrainCog className="mr-2" />
                            GPT 4
                        </div>
                    </SelectItem>
                    <SelectItem
                        className="font-montserrat font-semibold text-dinoBotPurple  focus:bg-dinoBotPurple rounded-2xl focus:text-white transition-all duration-300"
                        value="GPT 4 Turbo"
                    >
                        <div className="flex flex-row">
                            <BrainCircuit className="mr-2" />
                            GPT 4 Turbo
                        </div>
                    </SelectItem>
                    <SelectItem
                        className="font-montserrat font-semibold text-dinoBotRoseBonbon  focus:bg-dinoBotRoseBonbon rounded-2xl focus:text-white transition-all duration-300"
                        value="GPT 4-o"
                    >
                        <div className="flex flex-row">
                            <Brain className="mr-2" />
                            GPT 4-o
                        </div>
                    </SelectItem>
                </SelectGroup>
            </SelectContent>
        </Select>
    )
}
