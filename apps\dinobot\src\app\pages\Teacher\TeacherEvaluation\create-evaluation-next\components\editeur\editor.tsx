import React, { useEffect, useRef, useState } from 'react'
import ReactQuill from 'react-quill-new'
import 'react-quill-new/dist/quill.snow.css'
import EditorCortexDialog from './cortex-dialog'
import * as katex from 'katex'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { cn } from '@dinobot/utils'

window.katex = katex

const modules = {
    toolbar: [
        [{ header: '1' }, { header: '2' }, { font: [] }],
        [{ size: [] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [{ list: 'ordered' }, { indent: '-1' }, { indent: '+1' }],
        ['link', 'image', 'video'],
        ['clean']
    ],
    clipboard: {
        matchVisual: true
    },
    keyboard: {
        bindings: {}
    }
}
type ContentElement = {
    insert: string | { formula: string }
}

interface Props {
    theme: string
    value: string
    placeholder?: string
    onChange: (value: string, text: string) => void
    className?: string
    cortexOpen: boolean
    onCortexClose: () => void
    onFocuseOut: () => void
    isAIupdate?: boolean
}

function EditorQuill({
    theme = 'snow',
    placeholder = 'Write something...',
    onChange,
    value: val,
    className,
    onCortexClose,
    onFocuseOut,
    cortexOpen = false,
    isAIupdate = false
}: Props) {
    const [formats] = useState([
        'header',
        'font',
        'size',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'indent',
        'link',
        'image',
        'video',
        'formula',
        'align'
    ])
    const quillRef = useRef<ReactQuill>(null)
    const [value, setValue] = useState(val || '')
    const [, setText] = useState('')
    const {i18n} = useTranslation()
    const dir = getLangDir(i18n.language)

    const getText = (contents: ContentElement[]) => {
        let text = ''
        contents.forEach((el: ContentElement) => {
            if (typeof el.insert === 'object' && el.insert.formula) {
                text += `$${el.insert.formula}$`
            } else if (typeof el.insert === 'string') {
                text += el.insert
            }
        })
        return text
    }

    const insertFormula = (formula: string) => {
        const editor = quillRef.current?.getEditor()
        const range = editor?.getSelection(true)
        if (range) {
            editor?.insertEmbed(range.index, 'formula', `${formula}`)
            editor?.setSelection({ index: range.index + 1, length: 0 })
        }
    }

    const handleChange = (
        value: React.SetStateAction<string>,
        delta: any,
        source: any,
        editor: { getContents: () => any }
    ) => {
        // console.log(value,delta,source,editor)
        // setValue(value)
        // // console.log(quillRef.current.getEditor().insertEmbed(0, 'formula', "e=mc^2"))
        const text = getText(editor.getContents())
        setText(text)
        setValue(value)
        onChange(value.toString(), text)
    }
    useEffect(() => {
        if (isAIupdate) setValue(val)
    }, [val])

    return (
        <div className="relative w-full">
            <ReactQuill
                theme={theme}
                onChange={handleChange}
                defaultValue={val}
                value={value}
                modules={modules}
                formats={formats}
                bounds={'.app'}
                onBlur={onFocuseOut}
                placeholder={placeholder}
                ref={quillRef}
                className={cn(className, dir)}
            />
            <EditorCortexDialog
                open={cortexOpen}
                onInsert={insertFormula}
                onClose={onCortexClose}
            />
        </div>
    )
}

export default EditorQuill
