import * as React from 'react'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue
} from '@dinobot/components-ui'
import Cookies from 'js-cookie'
import { selectUseTopicStore } from '@dinobot/stores'
import {useExoModeStore} from '@dinobot/stores'
import {useExamsStore} from '@dinobot/stores'
import { toast } from 'sonner'
import {useAccountStore} from '@dinobot/stores'
import './select.css'
import { selectUseLocalStorageStore } from '@dinobot/stores'
import { getLangProps } from '@dinobot/utils'
import { Domain, User } from '@dinobot/prisma'
import { useLocation, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useHeader } from '../../../hooks/useHeader'

export const SelectTopic = ({ user: us }: { user: User }) => {
    const navigate = useNavigate()
    const { t , i18n} = useTranslation(['app/headers'],{keyPrefix:"topic"})
    const topic = Cookies.get('topic')
    const feature = Cookies.get('feature')
    const path = useLocation().pathname
    const setTopic = selectUseTopicStore.use.setTopic()
    const reset = useExoModeStore(state => state.reset)
    const { setopenExamsPopup, setExercise } = useExamsStore()
    const { setUser } = useAccountStore()
    const setDomainId = selectUseLocalStorageStore.use.setDomainId()
    const [oldLevelId, setOldLevelId] = React.useState<number | undefined>(us.levelId||undefined)
    
    // Utiliser le hook useHeader pour récupérer les domaines
    const { domains, domainsLoading, domainsError } = useHeader()

    React.useEffect(() => {
        setUser(us)
    }, [us,setUser])
    React.useEffect(() => {
        if (domains.length > 0 && (!topic || oldLevelId !== us.levelId)) {
            const firstDomain = domains[0]
            Cookies.set('topic', firstDomain?.name)
            Cookies.set('topicId', firstDomain?.id.toString())
            setDomainId(firstDomain?.id)
            setTopic(firstDomain as Domain)
            setOldLevelId(us.levelId||undefined)
        }
    }, [domains, topic, oldLevelId, setDomainId, setTopic, us.levelId])
    
    // Afficher une erreur si la récupération échoue
    React.useEffect(() => {
        if (domainsError) {
            toast.error('Erreur lors de la récupération des matières')
        }
    }, [domainsError])
    
    React.useEffect(() => {
        if (topic && domains.length > 0) {
            const domain = domains.find(d => d.name === topic)
            if (domain) {
                Cookies.set('topic', domain.name)
                Cookies.set('topicId', domain.id.toString())
                setDomainId(domain.id)
                setTopic(domain as Domain)
            }
        }
    }, [topic, domains, setDomainId, setTopic])
    
    const getDomain = () => {
        return domains.find(domain => domain.name === topic)
    }

    const handleTopicChange = (value: string) => {
        const domain = JSON.parse(value) as Domain

        try {
            Cookies.set('topic', domain.name)
            Cookies.set('topicId', domain.id.toString())
            setDomainId(domain.id)
            // setTopic(domain)
        } catch (err) {
            console.log('error: ' + err)
        } finally {
            switch (feature) {
                case 'Exo':
                    reset()
                    navigate('/exercise')
                    break
                case 'Exam':
                    setExercise(null)
                    navigate('/exam')
                    setopenExamsPopup(true)
                    break
                case 'Ctrl':
                    navigate('/create-controle')
                    break
                case 'Chat':
                    navigate('/')
                    break
                default:
                    navigate(path)
                    break
            }
            //setMessages([])
        }
    }
    const locale = i18n.language

    // Afficher un loader pendant le chargement
    if (domainsLoading) {
        return (
            <div className="w-[40vw] md:w-[20vw] h-10 bg-gray-200 rounded-xl animate-pulse" />
        )
    }
    
    return (
        <Select
            value={JSON.stringify(getDomain()) as string}
            onValueChange={handleTopicChange}
            disabled={domains.length === 0}
        >
            <SelectTrigger
                className={`font-montserrat font-semibold  w-[40vw] md:w-[20vw] flex flex-row justify-center sm:text-base rounded-xl text-white border animate-fade-in-left transition-all duration-300 selected`}
                style={{ ['--color' as string]: getDomain()?.color || '#000' }}
            >
                <SelectValue
                    placeholder={t('select')}
                    className="group/trigger *:stroke-white bg-red-500"
                />
            </SelectTrigger>
            <SelectContent className="rounded-2xl">
                <SelectGroup>
                    <SelectLabel>{t('module')}</SelectLabel>
                    {domains.map(domain => (
                        <SelectItem
                            key={domain.name}
                            className={`font-montserrat font-semibold rounded-2xl focus:text-white transition-all duration-300 select-color cursor-pointer`}
                            value={JSON.stringify(domain)}
                            style={{ ['--color' as string]: domain.color || '#000' }}
                        >
                            <div className="flex flex-row items-center gap-3">
                                {getLangProps({
                                    obj: domain,
                                    base: 'name',
                                    lang: locale
                                })}
                            </div>
                        </SelectItem>
                    ))}
                </SelectGroup>
            </SelectContent>
        </Select>
    )
}
