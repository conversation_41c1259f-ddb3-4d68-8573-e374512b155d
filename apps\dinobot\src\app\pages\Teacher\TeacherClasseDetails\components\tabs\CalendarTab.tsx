import React, { useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Calendar, dateFnsLocalizer, Navigate, Views } from 'react-big-calendar'
import { format, parse, startOfWeek, getDay } from 'date-fns'
import { fr, enUS } from 'date-fns/locale'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import { useClassDetailsStore } from '../../stores/ClassDetails.store'
import { useCalendarEvents } from '../../hooks/useCalendarEvents'
import HeaderCalendar from './HeaderCalendar'
import EventCalendarElement from './EventCalendarElement'

interface CalendarTabProps {
  classId: string
}

interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  color: string
  type: 'homework' | 'exam' | 'event'
}

const CalendarTab: React.FC<CalendarTabProps> = ({ classId }) => {
  const { t, i18n } = useTranslation('teacher-class-details')
  const [currentDate, setCurrentDate] = useState(new Date())
  const [myEvents, setMyEvents] = useState<CalendarEvent[]>([])
  const { currentClass } = useClassDetailsStore()
  
  const { data: eventsData, isLoading } = useCalendarEvents(classId)

  const locale = i18n.language === 'fr' ? fr : enUS
  const direction = i18n.dir()

  const localizer = dateFnsLocalizer({
    format,
    parse,
    startOfWeek: (date) => startOfWeek(date, { locale }),
    getDay,
    locales: {
      [i18n.language]: locale
    }
  })

  useEffect(() => {
    if (eventsData) {
      const formattedEvents: CalendarEvent[] = eventsData.map((event: any) => ({
        id: event.id,
        title: event.name || event.title,
        start: new Date(event.availableDate || event.start),
        end: new Date(event.dueDate || event.end),
        color: event.color || '#3174ad',
        type: event.type || 'event'
      }))
      setMyEvents(formattedEvents)
    }
  }, [eventsData])

  const eventStyleGetter = (event: CalendarEvent) => {
    const backgroundColor = event.color || '#3174ad'
    const style = {
      backgroundColor,
      borderRadius: '5px',
      opacity: 0.8,
      color: 'white',
      border: '0px',
      display: 'block'
    }
    return { style }
  }

  const handleNavigate = useCallback((newDate: Date, view: any, action: string) => {
    let newCurrentDate = currentDate

    switch (action) {
      case Navigate.PREVIOUS:
        newCurrentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
        break
      case Navigate.NEXT:
        newCurrentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
        break
      case Navigate.TODAY:
        newCurrentDate = new Date()
        break
      case Navigate.DATE:
        newCurrentDate = newDate
        break
      default:
        break
    }
    setCurrentDate(newCurrentDate)
  }, [currentDate])

  if (isLoading) {
    return <div>{t('status.loading')}</div>
  }

  return (
    <div className="h-full">
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          {t('calendar.title')} - {currentClass?.name}
        </h2>
      </div>
      
      <div className="h-[500px]">
        <Calendar
          localizer={localizer}
          events={myEvents}
          startAccessor="start"
          endAccessor="end"
          style={{ height: '100%' }}
          defaultView={Views.MONTH}
          views={[Views.MONTH, Views.WEEK, Views.DAY]}
          selectable
          components={{
            toolbar: HeaderCalendar,
            event: EventCalendarElement
          }}
          onNavigate={handleNavigate}
          date={currentDate}
          rtl={direction === 'rtl'}
          eventPropGetter={eventStyleGetter}
          messages={{
            next: t('calendar.next'),
            previous: t('calendar.previous'),
            today: t('calendar.today'),
            month: t('calendar.month'),
            week: t('calendar.week'),
            day: t('calendar.day'),
            agenda: t('calendar.agenda'),
            date: t('calendar.date'),
            time: t('calendar.time'),
            event: t('calendar.event'),
            noEventsInRange: t('calendar.no_events'),
            allDay: t('calendar.all_day')
          }}
        />
      </div>
    </div>
  )
}

export default CalendarTab