{"mathsExampleMessages": {"1": {"heading": "Explain the concept", "subheading": "...of function limits", "message": "Explain the concept of function limits", "animation": "animate-fade-in-top-left"}, "2": {"heading": "How to calculate", "subheading": "...the derivative of a function?", "message": "How to calculate the derivative of a function?", "animation": "animate-fade-in-top-right"}, "3": {"heading": "How to solve", "subheading": "...a system of linear equations with two variables?", "message": "How to solve a system of linear equations with two variables?", "animation": "animate-fade-in-bottom-left"}, "4": {"heading": "What is", "subheading": "...<PERSON><PERSON><PERSON>'s theorem?", "message": "What is <PERSON><PERSON><PERSON>'s theorem?", "animation": "animate-fade-in-bottom-right"}}, "physicsExampleMessages": {"5": {"heading": "What are", "subheading": "...the fundamental forces?", "message": "What are the fundamental forces?", "animation": "animate-fade-in-top-left"}, "6": {"heading": "Define", "subheading": "...the concept of work?", "message": "Define the concept of work?", "animation": "animate-fade-in-top-right"}, "7": {"heading": "What is", "subheading": "...electromagnetic radiation?", "message": "What is electromagnetic radiation?", "animation": "animate-fade-in-bottom-left"}, "8": {"heading": "What is", "subheading": "...the scientific explanation for time travel?", "message": "What is the scientific explanation for time travel?", "animation": "animate-fade-in-bottom-right"}}, "chemistryExampleMessages": {"141": {"heading": "Define", "subheading": "...a covalent bond.", "message": "Define a covalent bond.", "animation": "animate-fade-in-top-left"}, "142": {"heading": "Balance", "subheading": "...a chemical equation.", "message": "Balance a chemical equation.", "animation": "animate-fade-in-top-right"}, "143": {"heading": "What is the difference", "subheading": "...between an acid and a base?", "message": "What is the difference between an acid and a base?", "animation": "animate-fade-in-bottom-left"}, "144": {"heading": "What is", "subheading": "...radioactivity?", "message": "What is radioactivity?", "animation": "animate-fade-in-bottom-right"}}, "physicsChemistryExampleMessages": {"125": {"heading": "What are", "subheading": "...the fundamental forces?", "message": "What are the fundamental forces?", "animation": "animate-fade-in-top-left"}, "126": {"heading": "Define", "subheading": "...the concept of work?", "message": "Define the concept of work?", "animation": "animate-fade-in-top-right"}, "127": {"heading": "What is the difference", "subheading": "...between an acid and a base?", "message": "What is the difference between an acid and a base?", "animation": "animate-fade-in-bottom-left"}, "128": {"heading": "What is", "subheading": "...radioactivity?", "message": "What is radioactivity?", "animation": "animate-fade-in-bottom-right"}}, "svtExampleMessages": {"149": {"heading": "Explain the role", "subheading": "...of photosynthesis?", "message": "Explain the role of photosynthesis?", "animation": "animate-fade-in-top-left"}, "150": {"heading": "Briefly explain", "subheading": "what mitosis is?", "message": "Briefly explain what mitosis is?", "animation": "animate-fade-in-top-right"}, "151": {"heading": "What is the function", "subheading": "...of the immune system?", "message": "What is the function of the immune system?", "animation": "animate-fade-in-bottom-left"}, "152": {"heading": "What are", "subheading": "...biogeochemical cycles? Give a simple example.", "message": "What are biogeochemical cycles? Give a simple example.", "animation": "animate-fade-in-bottom-right"}}, "otherExampleMessages": {"145": {"heading": "Who was", "subheading": "...<PERSON>?", "message": "Who was <PERSON>?", "animation": "animate-fade-in-top-left"}, "146": {"heading": "What is the capital", "subheading": "...of Vietnam?", "message": "What is the capital of Vietnam?", "animation": "animate-fade-in-top-right"}, "147": {"heading": "What is the difference", "subheading": "...between a qualifying and a possessive adjective?", "message": "What is the difference between a qualifying and a possessive adjective?", "animation": "animate-fade-in-bottom-left"}, "148": {"heading": "What are", "subheading": "...the main rights of a citizen?", "message": "What are the main responsibilities of a citizen?", "animation": "animate-fade-in-bottom-right"}}, "historyExampleMessages": {"153": {"heading": "What are", "subheading": "...the causes of World War I?", "message": "What are the causes of World War I?", "animation": "animate-fade-in-top-left"}, "154": {"heading": "Explain the role", "subheading": "...of <PERSON> in the history of France", "message": "Explain the role of <PERSON> in the history of France", "animation": "animate-fade-in-top-right"}, "155": {"heading": "What is", "subheading": "...the Industrial Revolution?", "message": "What is the Industrial Revolution?", "animation": "animate-fade-in-bottom-left"}, "156": {"heading": "What were", "subheading": "...the main events of the French Revolution?", "message": "What were the main events of the French Revolution?", "animation": "animate-fade-in-bottom-right"}}, "emcExampleMessages": {"157": {"heading": "What is", "subheading": "...secularism in France?", "message": "What is secularism in France?", "animation": "animate-fade-in-top-left"}, "158": {"heading": "How are", "subheading": "...human rights protected?", "message": "How are human rights protected?", "animation": "animate-fade-in-top-right"}, "159": {"heading": "Explain the concept", "subheading": "...of participatory democracy", "message": "Explain the concept of participatory democracy", "animation": "animate-fade-in-bottom-left"}, "160": {"heading": "What are", "subheading": "...the duties of French citizens?", "message": "What are the duties of French citizens?", "animation": "animate-fade-in-bottom-right"}}, "frenchExampleMessages": {"129": {"heading": "How to analyze", "subheading": "...a literary work?", "message": "How to analyze a literary work?", "animation": "animate-fade-in-top-left"}, "130": {"heading": "What are", "subheading": "...the different literary genres?", "message": "What are the different literary genres?", "animation": "animate-fade-in-top-right"}, "131": {"heading": "Explain the rules", "subheading": "...of the sequence of tenses", "message": "Explain the rules of the sequence of tenses", "animation": "animate-fade-in-bottom-left"}, "132": {"heading": "How to write", "subheading": "...an essay?", "message": "How to write an essay?", "animation": "animate-fade-in-bottom-right"}}, "geographyExampleMessages": {"133": {"heading": "What are", "subheading": "...the main causes of climate change?", "message": "What are the main causes of climate change?", "animation": "animate-fade-in-top-left"}, "134": {"heading": "Explain the phenomenon", "subheading": "...of tides?", "message": "Explain the phenomenon of tides?", "animation": "animate-fade-in-top-right"}, "135": {"heading": "What is", "subheading": "...sustainable development?", "message": "What is sustainable development?", "animation": "animate-fade-in-bottom-left"}, "136": {"heading": "What are", "subheading": "...the different types of climates on Earth?", "message": "What are the different types of climates on Earth?", "animation": "animate-fade-in-bottom-right"}}, "histoireGeoExampleMessages": {"137": {"heading": "Explain the issues", "subheading": "...of globalization", "message": "Explain the issues of globalization", "animation": "animate-fade-in-top-left"}, "138": {"heading": "Describe the impact", "subheading": "...of European colonization on Africa", "message": "Describe the impact of European colonization on Africa", "animation": "animate-fade-in-top-right"}, "139": {"heading": "What were the consequences", "subheading": "...of the Cold War on Europe?", "message": "What were the consequences of the Cold War on Europe?", "animation": "animate-fade-in-bottom-left"}, "140": {"heading": "Explain the causes", "subheading": "...and consequences of rural exodus in France", "message": "Explain the causes and consequences of rural exodus in France", "animation": "animate-fade-in-bottom-right"}}, "philosophieExampleMessages": {"29": {"heading": "What is", "subheading": "...free will?", "message": "What is free will?", "animation": "animate-fade-in-top-left"}, "30": {"heading": "Can we", "subheading": "...know everything?", "message": "Can we know everything?", "animation": "animate-fade-in-top-right"}, "31": {"heading": "What is the difference", "subheading": "...between morality and ethics?", "message": "What is the difference between morality and ethics?", "animation": "animate-fade-in-bottom-left"}, "32": {"heading": "How to define", "subheading": "...happiness?", "message": "How to define happiness?", "animation": "animate-fade-in-bottom-right"}}, "languesExampleMessages": {"33": {"heading": "How can I", "subheading": "...improve my English pronunciation?", "message": "How can I improve my English pronunciation?", "animation": "animate-fade-in-top-left"}, "34": {"heading": "What is the difference", "subheading": "...between \"to know\" and \"to learn\"?", "message": "What is the difference between \"to know\" and \"to learn\"?", "animation": "animate-fade-in-top-right"}, "35": {"heading": "How to use", "subheading": "...past tenses in English?", "message": "How to use past tenses in English?", "animation": "animate-fade-in-bottom-left"}, "36": {"heading": "Explain", "subheading": "...the conditional in Spanish", "message": "Explain the conditional in Spanish", "animation": "animate-fade-in-bottom-right"}}, "sesExampleMessages": {"37": {"heading": "What are", "subheading": "...the factors of economic growth?", "message": "What are the factors of economic growth?", "animation": "animate-fade-in-top-left"}, "38": {"heading": "Explain", "subheading": "...the concept of marginal utility", "message": "Explain the concept of marginal utility", "animation": "animate-fade-in-top-right"}, "39": {"heading": "What is", "subheading": "...social mobility?", "message": "What is social mobility?", "animation": "animate-fade-in-bottom-left"}, "40": {"heading": "How does", "subheading": "...inflation affect purchasing power?", "message": "How does inflation affect purchasing power?", "animation": "animate-fade-in-bottom-right"}}, "microeconomieExampleMessages": {"161": {"heading": "Explain the theory", "subheading": "...of supply and demand", "message": "Explain the theory of supply and demand", "animation": "animate-fade-in-top-left"}, "162": {"heading": "What is", "subheading": "...price elasticity of demand?", "message": "What is price elasticity of demand?", "animation": "animate-fade-in-top-right"}, "163": {"heading": "How does", "subheading": "...profit maximization work?", "message": "How does profit maximization work?", "animation": "animate-fade-in-bottom-left"}, "164": {"heading": "What are", "subheading": "...economies of scale?", "message": "What are economies of scale?", "animation": "animate-fade-in-bottom-right"}}, "informatiqueExampleMessages": {"41": {"heading": "What is", "subheading": "...object-oriented programming?", "message": "What is object-oriented programming?", "animation": "animate-fade-in-top-left"}, "42": {"heading": "How does", "subheading": "...a sorting algorithm work?", "message": "How does a sorting algorithm work?", "animation": "animate-fade-in-top-right"}, "43": {"heading": "Explain", "subheading": "...the difference between HTTP and HTTPS", "message": "Explain the difference between HTTP and HTTPS", "animation": "animate-fade-in-bottom-left"}, "44": {"heading": "What is", "subheading": "...a markup language like HTML?", "message": "What is a markup language like HTML?", "animation": "animate-fade-in-bottom-right"}}, "artsExampleMessages": {"45": {"heading": "What are", "subheading": "...the characteristics of the Impressionist movement?", "message": "What are the characteristics of the Impressionist movement?", "animation": "animate-fade-in-top-left"}, "46": {"heading": "Explain", "subheading": "...perspective in drawing", "message": "Explain perspective in drawing", "animation": "animate-fade-in-top-right"}, "47": {"heading": "What is", "subheading": "...the pentatonic scale in music?", "message": "What is the pentatonic scale in music?", "animation": "animate-fade-in-bottom-left"}, "48": {"heading": "How to analyze", "subheading": "...a contemporary artwork?", "message": "How to analyze a contemporary artwork?", "animation": "animate-fade-in-bottom-right"}}, "spanishExampleMessages": {"121": {"heading": "¿Qué es", "subheading": "...el pretérito perfecto?", "message": "¿Qué es el pretérito perfecto?", "animation": "animate-fade-in-top-left"}, "122": {"heading": "¿Cómo se usan", "subheading": "...los verbos reflexivos?", "message": "¿Cómo se usan los verbos reflexivos?", "animation": "animate-fade-in-top-right"}, "123": {"heading": "Explica la diferencia", "subheading": "...entre \"ser\" y \"estar\".", "message": "Explica la diferencia entre \"ser\" y \"estar\".", "animation": "animate-fade-in-bottom-left"}, "124": {"heading": "¿Cómo escribir", "subheading": "...una carta formal en español?", "message": "¿Cómo escribir una carta formal en español?", "animation": "animate-fade-in-bottom-right"}}, "allemandExampleMessages": {"49": {"heading": "<PERSON>ie benutzt man", "subheading": "...den Konjunktiv II?", "message": "Wie benutzt man den Konjunktiv II?", "animation": "animate-fade-in-top-left"}, "50": {"heading": "Erkläre den Unterschied", "subheading": "...zwischen \"weil\" und \"denn\".", "message": "Erkläre den Unterschied zwischen \"weil\" und \"denn\".", "animation": "animate-fade-in-top-right"}, "51": {"heading": "Was ist", "subheading": "...die deutsche Wortstellung?", "message": "Was ist die deutsche Wortstellung?", "animation": "animate-fade-in-bottom-left"}, "52": {"heading": "Wie schreibt man", "subheading": "...einen formellen Brief auf Deutsch?", "message": "Wie schreibt man einen formellen Brief auf Deutsch?", "animation": "animate-fade-in-bottom-right"}}, "italienExampleMessages": {"53": {"heading": "Come si usa", "subheading": "...il congiuntivo presente?", "message": "Come si usa il congiuntivo presente?", "animation": "animate-fade-in-top-left"}, "54": {"heading": "Qual è la differenza", "subheading": "...tra \"essere\" e \"stare\"?", "message": "Qual è la differenza tra \"essere\" e \"stare\"?", "animation": "animate-fade-in-top-right"}, "55": {"heading": "Come si formano", "subheading": "...i tempi composti?", "message": "Come si formano i tempi composti?", "animation": "animate-fade-in-bottom-left"}, "56": {"heading": "Come scrivere", "subheading": "...una lettera formale in italiano?", "message": "Come scrivere una lettera formale in italiano?", "animation": "animate-fade-in-bottom-right"}}, "sciencesIngenieurExampleMessages": {"57": {"heading": "What is", "subheading": "...systemic modeling?", "message": "What is systemic modeling?", "animation": "animate-fade-in-top-left"}, "58": {"heading": "How does", "subheading": "...a control system work?", "message": "How does a control system work?", "animation": "animate-fade-in-top-right"}, "59": {"heading": "Explain", "subheading": "...the principle of material resistance", "message": "Explain the principle of material resistance", "animation": "animate-fade-in-bottom-left"}, "60": {"heading": "How to create", "subheading": "...a Gantt chart?", "message": "How to create a Gantt chart?", "animation": "animate-fade-in-bottom-right"}}, "humanitesLitteraturePhilosophieExampleMessages": {"61": {"heading": "How to analyze", "subheading": "...a philosophical text?", "message": "How to analyze a philosophical text?", "animation": "animate-fade-in-top-left"}, "62": {"heading": "What is", "subheading": "...the existentialist movement?", "message": "What is the existentialist movement?", "animation": "animate-fade-in-top-right"}, "63": {"heading": "Explain", "subheading": "...Renaissance humanism", "message": "Explain Renaissance humanism", "animation": "animate-fade-in-bottom-left"}, "64": {"heading": "What is the difference", "subheading": "...between rhetoric and argumentation?", "message": "What is the difference between rhetoric and argumentation?", "animation": "animate-fade-in-bottom-right"}}, "hgGeoPolSciPolExampleMessages": {"65": {"heading": "Explain", "subheading": "...the concept of soft power", "message": "Explain the concept of soft power", "animation": "animate-fade-in-top-left"}, "66": {"heading": "What are", "subheading": "...the geopolitical issues in the Middle East?", "message": "What are the geopolitical issues in the Middle East?", "animation": "animate-fade-in-top-right"}, "67": {"heading": "How does", "subheading": "...the United Nations work?", "message": "How does the United Nations work?", "animation": "animate-fade-in-bottom-left"}, "68": {"heading": "What is", "subheading": "...multilateralism?", "message": "What is multilateralism?", "animation": "animate-fade-in-bottom-right"}}, "sciencesTechnologieExampleMessages": {"69": {"heading": "What is", "subheading": "...artificial intelligence?", "message": "What is artificial intelligence?", "animation": "animate-fade-in-top-left"}, "70": {"heading": "How does", "subheading": "...an electric motor work?", "message": "How does an electric motor work?", "animation": "animate-fade-in-top-right"}, "71": {"heading": "Explain", "subheading": "...how a printed circuit board works", "message": "Explain how a printed circuit board works", "animation": "animate-fade-in-bottom-left"}, "72": {"heading": "What are", "subheading": "...the different renewable energy sources?", "message": "What are the different renewable energy sources?", "animation": "animate-fade-in-bottom-right"}}, "lcleExampleMessages": {"73": {"heading": "How can I analyze", "subheading": "...a literary text in English?", "message": "How can I analyze a literary text in English?", "animation": "animate-fade-in-top-left"}, "74": {"heading": "What is", "subheading": "...the American Dream concept?", "message": "What is the American Dream concept?", "animation": "animate-fade-in-top-right"}, "75": {"heading": "Explain", "subheading": "...Shakespeare's influence on modern literature", "message": "Explain <PERSON>'s influence on modern literature", "animation": "animate-fade-in-bottom-left"}, "76": {"heading": "How do", "subheading": "...cultural differences impact communication?", "message": "How do cultural differences impact communication?", "animation": "animate-fade-in-bottom-right"}}, "enseignementScientifiqueExampleMessages": {"77": {"heading": "What is", "subheading": "...the greenhouse effect?", "message": "What is the greenhouse effect?", "animation": "animate-fade-in-top-left"}, "78": {"heading": "How does", "subheading": "...the solar system work?", "message": "How does the solar system work?", "animation": "animate-fade-in-top-right"}, "79": {"heading": "Explain", "subheading": "...biodiversity and its importance", "message": "Explain biodiversity and its importance", "animation": "animate-fade-in-bottom-left"}, "80": {"heading": "What is", "subheading": "...DNA and how does it work?", "message": "What is DNA and how does it work?", "animation": "animate-fade-in-bottom-right"}}, "sntExampleMessages": {"81": {"heading": "What is", "subheading": "...Big Data?", "message": "What is Big Data?", "animation": "animate-fade-in-top-left"}, "82": {"heading": "How does", "subheading": "...a computer network work?", "message": "How does a computer network work?", "animation": "animate-fade-in-top-right"}, "83": {"heading": "Explain", "subheading": "...cybersecurity and its challenges", "message": "Explain cybersecurity and its challenges", "animation": "animate-fade-in-bottom-left"}, "84": {"heading": "What is", "subheading": "...cloud computing?", "message": "What is cloud computing?", "animation": "animate-fade-in-bottom-right"}}, "mathComplementairesExampleMessages": {"85": {"heading": "How to solve", "subheading": "...a quadratic equation?", "message": "How to solve a quadratic equation?", "animation": "animate-fade-in-top-left"}, "86": {"heading": "What is", "subheading": "...trigonometry?", "message": "What is trigonometry?", "animation": "animate-fade-in-top-right"}, "87": {"heading": "Explain", "subheading": "...the Pythagorean theorem and its applications", "message": "Explain the Pythagorean theorem and its applications", "animation": "animate-fade-in-bottom-left"}, "88": {"heading": "How to calculate", "subheading": "...a simple integral?", "message": "How to calculate a simple integral?", "animation": "animate-fade-in-bottom-right"}}, "mathExpertesExampleMessages": {"89": {"heading": "What are", "subheading": "...complex numbers?", "message": "What are complex numbers?", "animation": "animate-fade-in-top-left"}, "90": {"heading": "How to solve", "subheading": "...a differential equation?", "message": "How to solve a differential equation?", "animation": "animate-fade-in-top-right"}, "91": {"heading": "Explain", "subheading": "...the concept of infinite limit", "message": "Explain the concept of infinite limit", "animation": "animate-fade-in-bottom-left"}, "92": {"heading": "How to prove", "subheading": "...a mathematical theorem?", "message": "How to prove a mathematical theorem?", "animation": "animate-fade-in-bottom-right"}}, "mathApprofondiesExampleMessages": {"93": {"heading": "What is", "subheading": "...linear algebra?", "message": "What is linear algebra?", "animation": "animate-fade-in-top-left"}, "94": {"heading": "How to use", "subheading": "...matrices in mathematics?", "message": "How to use matrices in mathematics?", "animation": "animate-fade-in-top-right"}, "95": {"heading": "Explain", "subheading": "...<PERSON><PERSON>'s theorem and its applications", "message": "Explain <PERSON>'s theorem and its applications", "animation": "animate-fade-in-bottom-left"}, "96": {"heading": "How to calculate", "subheading": "...a primitive?", "message": "How to calculate a primitive?", "animation": "animate-fade-in-bottom-right"}}, "mathAppliquesExampleMessages": {"97": {"heading": "How to use", "subheading": "...statistics to analyze data?", "message": "How to use statistics to analyze data?", "animation": "animate-fade-in-top-left"}, "98": {"heading": "What are", "subheading": "...conditional probabilities?", "message": "What are conditional probabilities?", "animation": "animate-fade-in-top-right"}, "99": {"heading": "Explain", "subheading": "...linear programming", "message": "Explain linear programming", "animation": "animate-fade-in-bottom-left"}, "100": {"heading": "How to apply", "subheading": "...mathematics to economics?", "message": "How to apply mathematics to economics?", "animation": "animate-fade-in-bottom-right"}}, "financeExampleMessages": {"101": {"heading": "What is", "subheading": "...compound capitalization?", "message": "What is compound capitalization?", "animation": "animate-fade-in-top-left"}, "102": {"heading": "How to calculate", "subheading": "...the net present value of an investment?", "message": "How to calculate the net present value of an investment?", "animation": "animate-fade-in-top-right"}, "103": {"heading": "Explain", "subheading": "...how financial markets work", "message": "Explain how financial markets work", "animation": "animate-fade-in-bottom-left"}, "104": {"heading": "What is", "subheading": "...an interest rate?", "message": "What is an interest rate?", "animation": "animate-fade-in-bottom-right"}}, "physiqueExampleMessages": {"105": {"heading": "Explain", "subheading": "...the special theory of relativity", "message": "Explain the special theory of relativity", "animation": "animate-fade-in-top-left"}, "106": {"heading": "How does", "subheading": "...an electric circuit work?", "message": "How does an electric circuit work?", "animation": "animate-fade-in-top-right"}, "107": {"heading": "What is", "subheading": "...quantum mechanics?", "message": "What is quantum mechanics?", "animation": "animate-fade-in-bottom-left"}, "108": {"heading": "Explain", "subheading": "...<PERSON>'s laws", "message": "Explain <PERSON>'s laws", "animation": "animate-fade-in-bottom-right"}}, "technologieExampleMessages": {"109": {"heading": "How does", "subheading": "...a microprocessor work?", "message": "How does a microprocessor work?", "animation": "animate-fade-in-top-left"}, "110": {"heading": "What is", "subheading": "...3D printing?", "message": "What is 3D printing?", "animation": "animate-fade-in-top-right"}, "111": {"heading": "Explain", "subheading": "...how a Wi-Fi network works", "message": "Explain how a Wi-Fi network works", "animation": "animate-fade-in-bottom-left"}, "112": {"heading": "How to design", "subheading": "...a technical object?", "message": "How to design a technical object?", "animation": "animate-fade-in-bottom-right"}}, "nsiExampleMessages": {"113": {"heading": "What is", "subheading": "...a search algorithm?", "message": "What is a search algorithm?", "animation": "animate-fade-in-top-left"}, "114": {"heading": "How does", "subheading": "...a relational database work?", "message": "How does a relational database work?", "animation": "animate-fade-in-top-right"}, "115": {"heading": "Explain", "subheading": "...recursive programming", "message": "Explain recursive programming", "animation": "animate-fade-in-bottom-left"}, "116": {"heading": "What is", "subheading": "...algorithmic complexity?", "message": "What is algorithmic complexity?", "animation": "animate-fade-in-bottom-right"}}}