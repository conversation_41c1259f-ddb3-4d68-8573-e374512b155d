// src/pages/teacher/my-classes/components/CreateSelect.tsx
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { Button } from '@dinobot/components-ui'
import { Plus } from 'lucide-react'

const CreateSelect: React.FC = () => {
  const { t } = useTranslation('teacher-my-classes-about')
  const navigate = useNavigate()

  const handleCreateEvaluation = () => {
    navigate('./create-evaluation-next')
  }

  return (
    <Button
      onClick={handleCreateEvaluation}
      className="flex gap-2 rounded-full bg-dinoBotRedOrange text-dinoBotWhite py-2 px-4"
    >
      <Plus />
      {t('createEvaluation')}
    </Button>
  )
}

export default CreateSelect