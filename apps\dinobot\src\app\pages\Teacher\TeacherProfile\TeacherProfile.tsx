import React from 'react';
import { useTranslation } from 'react-i18next';
import TeacherProfileForm from './components/TeacherProfileForm';

const TeacherProfile: React.FC = () => {
    const { t } = useTranslation(['app/chat']);

    return (
        <div className="w-full flex flex-col justify-start overflow-y-auto">
            <div className="py-8 px-10 flex gap-3 items-center">
                <div className="text-4xl font-bold text-gray-700">
                    {t('profile.mycompte')}
                </div>
            </div>
            <div className="max-w-full px-10 flex items-center">
                <div className="md:w-[90%] flex flex-col px-10 overflow-y-auto">
                    <TeacherProfileForm />
                </div>
            </div>
        </div>
    );
};

export default TeacherProfile;
