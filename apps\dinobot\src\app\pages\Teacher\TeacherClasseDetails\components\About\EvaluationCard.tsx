// src/pages/teacher/my-classes/components/EvaluationCard.tsx
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { EllipsisVertical, Home, Timer } from 'lucide-react'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@dinobot/components-ui'
import { EvaluationCardProps } from '../../about-tab.types'
import EvaluationPopover from './EvaluationPopover'

const EvaluationCard: React.FC<EvaluationCardProps> = ({
  evaluation,
  isEvalOrTask = true
}) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  const [open, setOpen] = useState(false)

  return (
    <div
      className={`border-2 rounded-lg ${
        isEvalOrTask 
          ? 'border-dinoBotRedOrange/60' 
          : 'border-dinoBotVibrantBlue/60'
      } w-72 min-h-28 p-2`}
    >
      <div className="flex justify-between items-center">
        <div className="flex gap-2 items-center">
          <h3
            className={`${
              isEvalOrTask 
                ? 'text-dinoBotRedOrange' 
                : 'text-dinoBotVibrantBlue'
            } flex gap-2`}
          >
            {isEvalOrTask ? (
              <>
                <Timer />
                <span className="mt-auto">
                  {t('eval_card.eval', { ns: 'teacher/my-class/apropo' })}
                </span>
              </>
            ) : (
              <>
                <Home />
                <span className="mt-auto">
                  {t('eval_card.exo', { ns: 'teacher/my-class/apropo' })}
                </span>
              </>
            )}
          </h3>
        </div>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger className="rounded-full shadow-lg p-1">
            <EllipsisVertical />
          </PopoverTrigger>
          <PopoverContent className="flex flex-col gap-2 w-fit pr-6">
            <EvaluationPopover evaluation={evaluation!} />
          </PopoverContent>
        </Popover>
      </div>
      <p className="text-dinoBotGray">
        {evaluation?.status === 'ASSIGNED' 
          ? t('eval_card.assigned', { ns: 'teacher/my-class/apropo' }) 
          : t('eval_card.draft', { ns: 'teacher/my-class/apropo' })
        }
      </p>
      <p className="text-wrap line-clamp-3">{evaluation?.name}</p>
    </div>
  )
}

export default EvaluationCard