import { Langfuse } from 'langfuse';
import { generateText, streamText, generateObject, GenerateTextResult, StreamTextResult, GenerateObjectResult } from 'ai';
import { logger } from '@/lib/logger';
import { randomUUID } from 'crypto';
import { z } from 'zod';

// Initialize Langfuse client
const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL || 'https://cloud.langfuse.com',
});

interface TracedAIOptions {
  traceId?: string;
  traceName?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  tags?: string[];
}

/**
 * Wrapper for generateText with Langfuse tracing
 */
export async function tracedGenerateText(
  options: Parameters<typeof generateText>[0],
  traceOptions?: TracedAIOptions
): Promise<GenerateTextResult<any, any>> {
  const traceId = traceOptions?.traceId || randomUUID();
  const traceName = traceOptions?.traceName || 'generate-text';

  // Create or update trace
  const trace = langfuse.trace({
    id: traceId,
    name: traceName,
    userId: traceOptions?.userId,
    sessionId: traceOptions?.sessionId,
    metadata: traceOptions?.metadata,
    tags: traceOptions?.tags,
  });

  try {
    // Enable telemetry for Vercel AI SDK
    const result = await generateText({
      ...options,
      experimental_telemetry: {
        isEnabled: true,
        functionId: traceName,
        metadata: {
          langfuseTraceId: traceId,
          ...traceOptions?.metadata,
        },
      },
    });

    // Update trace with success
    trace.update({
      output: result.text,
      metadata: {
        ...traceOptions?.metadata,
        model: options.model?.modelId || 'unknown',
        usage: result.usage,
        finishReason: result.finishReason,
      },
    });

    await langfuse.flushAsync();
    return result;
  } catch (error) {
    // Update trace with error
    trace.update({
      level: 'ERROR',
      statusMessage: error instanceof Error ? error.message : 'Unknown error',
    });
    
    await langfuse.flushAsync();
    throw error;
  }
}

/**
 * Wrapper for streamText with Langfuse tracing
 */
export async function tracedStreamText(
  options: Parameters<typeof streamText>[0],
  traceOptions?: TracedAIOptions
): Promise<StreamTextResult<any, any>> {
  const traceId = traceOptions?.traceId || randomUUID();
  const traceName = traceOptions?.traceName || 'stream-text';

  // Create trace
  const trace = langfuse.trace({
    id: traceId,
    name: traceName,
    userId: traceOptions?.userId,
    sessionId: traceOptions?.sessionId,
    metadata: traceOptions?.metadata,
    tags: traceOptions?.tags,
  });

  try {
    // Enable telemetry for Vercel AI SDK
    const result = await streamText({
      ...options,
      experimental_telemetry: {
        isEnabled: true,
        functionId: traceName,
        metadata: {
          langfuseTraceId: traceId,
          ...traceOptions?.metadata,
        },
      },
    });

    // We'll update the trace when the stream completes
    // For now, just log that streaming started
    trace.update({
      metadata: {
        ...traceOptions?.metadata,
        model: options.model?.modelId || 'unknown',
        streaming: true,
      },
    });

    await langfuse.flushAsync();
    return result;
  } catch (error) {
    // Update trace with error
    trace.update({
      level: 'ERROR',
      statusMessage: error instanceof Error ? error.message : 'Unknown error',
    });
    
    await langfuse.flushAsync();
    throw error;
  }
}

/**
 * Create a Langfuse generation span for prompt tracking
 */
export async function createGeneration(
  traceId: string,
  name: string,
  input: any,
  model?: string,
  metadata?: Record<string, any>
) {
  return langfuse.generation({
    traceId,
    name,
    input,
    model,
    metadata,
  });
}

/**
 * Wrapper for generateObject with Langfuse tracing
 */
export async function tracedGenerateObject<T>(
  options: Omit<Parameters<typeof generateObject>[0], 'schema'> & { schema: z.ZodType<T> },
  traceOptions?: TracedAIOptions
): Promise<GenerateObjectResult<T>> {
  const traceId = traceOptions?.traceId || randomUUID();
  const traceName = traceOptions?.traceName || 'generate-object';

  // Create trace
  const trace = langfuse.trace({
    id: traceId,
    name: traceName,
    userId: traceOptions?.userId,
    sessionId: traceOptions?.sessionId,
    metadata: traceOptions?.metadata,
    tags: traceOptions?.tags,
  });

  try {
    // Enable telemetry for Vercel AI SDK
    const result = await generateObject({
      ...options,
      experimental_telemetry: {
        isEnabled: true,
        functionId: traceName,
        metadata: {
          langfuseTraceId: traceId,
          ...traceOptions?.metadata,
        },
      },
    });

    // Update trace with success
    trace.update({
      output: result.object,
      metadata: {
        ...traceOptions?.metadata,
        model: options.model?.modelId || 'unknown',
        usage: result.usage,
        finishReason: result.finishReason,
      },
    });

    await langfuse.flushAsync();
    return result;
  } catch (error) {
    // Update trace with error
    trace.update({
      level: 'ERROR',
      statusMessage: error instanceof Error ? error.message : 'Unknown error',
    });
    
    await langfuse.flushAsync();
    throw error;
  }
}

/**
 * Flush Langfuse traces (useful for serverless environments)
 */
export async function flushTraces() {
  await langfuse.flushAsync();
}