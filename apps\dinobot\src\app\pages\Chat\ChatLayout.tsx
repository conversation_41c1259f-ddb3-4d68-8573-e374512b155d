import React from 'react'
import { Avatar<PERSON>loat,SubHeader,SidebarDesktop,FilesSidebarDesktop,CalculatorTrigger ,Calculator, withCheckRole} from '@dinobot/components-ui'
// TODO AUTH
// import { auth, getUser, signOut } from '@/auth'
import { type User,UserTypeSchema } from '@dinobot/prisma'
import { Outlet, useNavigate, useRouteLoaderData } from 'react-router-dom'
import EstablishmentSideBar from '../../layouts/SideBars/establishment-sidebar'
import StudentSidebar from '../../layouts/SideBars/student-sidebar'
import { Header } from './components/header/header'


export default withCheckRole([
    UserTypeSchema.enum.student,
    UserTypeSchema.enum.establishment
])( function ChatLayout() {
    const HEYGEN_AVATAR_KEY = import.meta.env.APP_HEYGEN_AVATAR_KEY || ''
    const HEYGEN_VOICE_ID = import.meta.env.APP_HEYGEN_VOICE_ID || ''
    const LoginMode =
        import.meta.env.APP_LOGIN_TYPE ===
        'dinobot'
            ? true
            : false
    const navigate = useNavigate()
    const {user}= useRouteLoaderData('user') as {user:User}
    const disconnect = async () => {
        // TODO logout 
        // await signOut()
        navigate('/')
    }
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden relative">
            <div className="bg-dinoBotBlue flex flex-col z-10 w-fit">
                {user?.type === UserTypeSchema.enum.establishment ? (
                    <EstablishmentSideBar
                        user={user}
                        logOut={disconnect}
                    />
                ) : (
                    <StudentSidebar />
                )}
            </div>
            <div className="flex flex-col size-full overflow-hidden #overflow-y-auto grow">
                <div className="sticky max-h-28 top-0 flex flex-col grow">
                    <Header />
                    {LoginMode && <SubHeader />}
                </div>
                <main className="flex flex-col flex-1 bg-muted/50 h-[calc(100vh-115px)] overflow-y-auto app-scroller ">
                    <div className="relative flex h-[calc(100vh_-_theme(spacing.20))] overflow-hidden">
                        <AvatarFloat
                            avatarKey={HEYGEN_AVATAR_KEY}
                            voiceId={HEYGEN_VOICE_ID}
                        />
                        <SidebarDesktop />
                        <Outlet />
                        <FilesSidebarDesktop />
                    </div>
                </main>
                <CalculatorTrigger />
                <Calculator />
            </div>
        </div>
    )
})