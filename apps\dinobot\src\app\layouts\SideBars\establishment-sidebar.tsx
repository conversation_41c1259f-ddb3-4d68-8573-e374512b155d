import {useAccountStore} from '@dinobot/stores'
import {
    ChevronRight,
    FolderOpen,
    Notebook,
    Settings
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { SelectIntl } from '@dinobot/components-ui'
import { User } from '@dinobot/prisma'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import DisconnectButton from './disconnect'

const EstablishmentLinks = [
    {
        icon: <Notebook />,
        label: 'workspace',
        links: [
            {
                name: 'student_space_view',
                path: '/exercise',
                activeIf: ['/exercise', '/exercise/train']
            }
        ]
    },
    {
        icon: <FolderOpen />,
        label: 'medias',
        links: [
            {
                name: 'content_management',
                path: '/manage-content/skills',
                activeIf: [
                    '/manage-content',
                    '/manage-content/skills',
                    '/manage-content/tasks',
                    '/manage-content/eval'
                ]
            },
            {
                name: 'logout',
                path: '/',
                activeIf: ['/']
            }
        ]
    }
]
type EstablishmentSideBarProps = { user: User; logOut: () => void }

function EstablishmentSideBar({ user, logOut }: EstablishmentSideBarProps) {
    const { t,i18n } = useTranslation(['establishment/sidebar'])
    const pathname = useLocation().pathname
    const dir = i18n.dir()
    const [active, setActive] = useState(true)
    const { setUser } = useAccountStore()

    useEffect(() => {
        setUser(user)
    }, [user, setUser])

    useEffect(() => {
        if (pathname.includes('student-view/train')) {
            setActive(false)
        }
    }, [pathname])

    return (
        <div
            className={`relative ${active ? 'w-80 px-10 py-2' : 'w-0'} bg-dinoBotDarkRed h-full transition-all duration-300 ease-in-out`}
        >
            <div className=" flex flex-col justify-between h-full overflow-hidden ">
                <div>
                    <SelectIntl />
                </div>
                <div className="w-96 flex flex-col gap-8 justify-between">
                    {EstablishmentLinks.map((item, index) => (
                        <div
                            key={index}
                            className=" text-white flex flex-col gap-2"
                        >
                            <div className="flex gap-2 items-center text-2xl font-extrabold text-nowrap">
                                {item.icon}
                                <p>{t(item.label)}</p>
                            </div>
                            <div className="flex flex-col text-lg font-light gap-1">
                                {item.links.map(link => {
                                    if (link.name === 'logout') {
                                        return (
                                            <DisconnectButton
                                                key={link.path}
                                                onClick={() => logOut()}
                                            />
                                        )
                                    }
                                    return (
                                        <Link
                                            className={`hover:underline transition-all duration-200 text-nowrap pl-2 ${link.activeIf.includes(pathname) ? 'font-semibold underline' : ''}`}
                                            to={link.path}
                                            key={link.path}
                                        >
                                            {t(link.name)}
                                        </Link>
                                    )
                                })}
                            </div>
                        </div>
                    ))}
                </div>
                <div>
                    <div className="size-10 rounded-full bg-dinoBotWhite text-dinoBotDarkRed flex justify-center items-center hover:text-dinoBotDarkRed/80 transition-all duration-300 cursor-pointer">
                        <Settings className="size-8" />
                    </div>
                </div>
            </div>

            <button
                className={`absolute ${dir === 'ltr' ? '-right-8 rounded-r-3xl' : '-left-8 rounded-l-3xl'} top-1/2 w-8 h-32 -translate-y-1/2 bg-dinoBotDarkRed flex items-center justify-center text-white pr-1 after:bg-transparent after:absolute after:size-4 after:z-auto after:-top-4 ${dir === 'ltr' ? 'after:left-0 after:rounded-bl-3xl after:shadow-[-5px_5px_0px_1px_#E10A15]' : 'after:right-0 after:rounded-br-3xl after:shadow-[5px_5px_0px_1px_#E10A15]'} before:bg-transparent before:absolute before:size-4 before:z-auto before:-bottom-4 ${dir === 'ltr' ? 'before:left-0 before:rounded-tl-3xl before:shadow-[-5px_-5px_0px_1px_#E10A15]' : 'before:right-0 before:rounded-tr-3xl before:shadow-[5px_-5px_0px_1px_#E10A15]'}`}
                onClick={() => {
                    setActive(a => !a)
                }}
            >
                <ChevronRight
                    className={`${active ? (dir === 'ltr' ? 'rotate-180' : '') : dir === 'ltr' ? 'rotate-0' : 'rotate-180'} transition duration-300 ease-in-out`}
                />
            </button>
        </div>
    )
}

export default EstablishmentSideBar
