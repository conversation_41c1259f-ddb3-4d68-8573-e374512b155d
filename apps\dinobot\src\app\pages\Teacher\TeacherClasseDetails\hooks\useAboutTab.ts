// src/pages/teacher/my-classes/hooks/useAboutTab.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  useApiClient,
  useAuthApiClient,
} from '../../../../contexts/AppContext';
import { ClassWithRelations, Theme, ControlPartial } from '../about-tab.types';

export const useAboutTab = (classId: string) => {
  const authApiClient = useAuthApiClient();
  const queryClient = useQueryClient();

  // Get class by ID
  const useClassQuery = () => {
    return useQuery({
      queryKey: ['class', classId],
      queryFn: async (): Promise<ClassWithRelations> => {
        const response = await authApiClient.get(`/api/classes/${classId}`);
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Get themes for class
  const useThemesQuery = () => {
    return useQuery({
      queryKey: ['themes', classId],
      queryFn: async (): Promise<Theme[]> => {
        const response = await authApiClient.get(
          `/api/themes/class/${classId}`,
        );
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Get class code
  const useClassCodeQuery = () => {
    return useQuery({
      queryKey: ['classCode', classId],
      queryFn: async (): Promise<{ code: string }> => {
        const response = await authApiClient.get(
          `/api/classes/${classId}/code`,
        );
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Get controls/evaluations for class
  const useControlsQuery = () => {
    return useQuery({
      queryKey: ['controls', classId],
      queryFn: async (): Promise<ControlPartial[]> => {
        const response = await authApiClient.get(
          `/api/classes/${classId}/controls`,
        );
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Get controls/evaluations for class
  const useControlsByThemeIdQuery = (themeId: string | null) => {
    return useQuery({
      queryKey: ['controls', themeId],
      queryFn: async (): Promise<ControlPartial[]> => {
        const response = await authApiClient.get(
          `/api/control-mode/theme/${themeId}`,
        );
        return response.data;
      },
      enabled: !!themeId,
    });
  };

  // Get control by ID
  const useControlByIdQuery = (controlId?: string) => {
    return useQuery({
      queryKey: ['control', controlId],
      queryFn: async (): Promise<ControlPartial> => {
        const response = await authApiClient.get(`/api/controls/${controlId}`);
        return response.data;
      },
      enabled: !!controlId,
    });
  };

  // Get control with media by ID
  const useControlWithMediaQuery = (controlId?: string) => {
    return useQuery({
      queryKey: ['controlWithMedia', controlId],
      queryFn: async (): Promise<ControlPartial> => {
        const response = await authApiClient.get(
          `/api/controls/${controlId}/with-media`,
        );
        return response.data;
      },
      enabled: !!controlId,
    });
  };

  // Get non-assigned controls for class
  const useNonAssignedControlsQuery = () => {
    return useQuery({
      queryKey: ['nonAssignedControls', classId],
      queryFn: async (): Promise<ControlPartial[]> => {
        const response = await authApiClient.get(
          `/api/classes/${classId}/controls/non-assigned`,
        );
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Get planned evaluations for class
  const usePlannedEvaluationsQuery = () => {
    return useQuery({
      queryKey: ['plannedEvaluations', classId],
      queryFn: async (): Promise<ControlPartial[]> => {
        const response = await authApiClient.get(
          `/api/classes/${classId}/planned-evaluations`,
        );
        return response.data;
      },
      enabled: !!classId,
    });
  };

  // Delete control mutation
  const useDeleteControlMutation = () => {
    return useMutation({
      mutationFn: async (controlId: string): Promise<void> => {
        await authApiClient.delete(`/api/controls/${controlId}`);
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['controls', classId] });
        queryClient.invalidateQueries({ queryKey: ['themes', classId] });
      },
    });
  };

  // Delete multiple controls mutation
  const useDeleteControlsMutation = () => {
    return useMutation({
      mutationFn: async (controlIds: string[]): Promise<void> => {
        await authApiClient.delete('/api/controls/batch', {
          data: { ids: controlIds },
        });
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['controls', classId] });
        queryClient.invalidateQueries({ queryKey: ['themes', classId] });
      },
    });
  };

  return {
    useClassQuery,
    useThemesQuery,
    useControlsQuery,
    useControlByIdQuery,
    useControlWithMediaQuery,
    useNonAssignedControlsQuery,
    usePlannedEvaluationsQuery,
    useDeleteControlMutation,
    useClassCodeQuery,
    useControlsByThemeIdQuery,
  };
};
