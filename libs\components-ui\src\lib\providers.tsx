//Hooks
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { SidebarProvider, FilesSidebarProvider } from '@dinobot/hooks'

//Themes
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { ThemeProviderProps } from 'next-themes'

//UI
import { Toaster, TooltipProvider } from '@dinobot/components-ui'

export function Providers({
    children,
    queryClient,
    ...props
}: ThemeProviderProps & { queryClient: QueryClient }) {
    const devtools = import.meta.env.APP_DEV_TOOL_QUERY==='true'
    return (
        <NextThemesProvider {...props}>
            <SidebarProvider>
                <FilesSidebarProvider>
                    <QueryClientProvider client={queryClient}>
                        {devtools && <ReactQueryDevtools />}
                        <TooltipProvider>{children}</TooltipProvider>
    					<Toaster position="top-center" />
                    </QueryClientProvider>
                </FilesSidebarProvider>
            </SidebarProvider>
        </NextThemesProvider>
    )
}
