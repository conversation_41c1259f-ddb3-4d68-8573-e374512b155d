import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

export const useCalendarEvents = (classId: string) => {
  const apiClient = useAuthApiClient()

  return useQuery({
    queryKey: ['calendar-events', classId],
    queryFn: async () => {
      const response = await apiClient.get(`/api/classes/${classId}/calendar-events`)
      return response.data
    },
    enabled: !!classId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}