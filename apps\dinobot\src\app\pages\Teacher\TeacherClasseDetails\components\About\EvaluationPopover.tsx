// src/pages/teacher/my-classes/components/EvaluationPopover.tsx
import React, { useReducer } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate, useLocation } from 'react-router-dom'
import { CalendarPlus2, <PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react'
import { Button, Skeleton } from '@dinobot/components-ui'
import { useParams } from 'react-router-dom'
import { useApropoStore } from '../../stores/AboutTab.store'
import { useAboutTab } from '../../hooks/useAboutTab'
import { ControlPartial, DialogState } from '../../about-tab.types'
import EvaluationAlert from './EvaluationAlert'
import ConfirmDialog from './ConfirmDialog'
import EvaluationElementPreview from './EvaluationElementPreview'

interface EvaluationPopoverProps {
  evaluation: ControlPartial
}

// Helper function to check if any evaluation is assigned
function isAnyEvaluationAssigned(
  evaluationId: string | undefined,
  controls: ControlPartial[] = []
): boolean {
  return controls.some(
    control =>
      evaluationId === control.id && control.status === 'ASSIGNED'
  )
}

const initialState: DialogState = {
  deleteDialogOpen: false,
  evaluationAlertOpen: false
}

const EvaluationPopover: React.FC<EvaluationPopoverProps> = ({ evaluation }) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  
  const { classId } = useParams<{ classId: string }>()
  const navigate = useNavigate()
  const location = useLocation()
  
  // Get cookies (you'll need to implement this based on your cookie solution)
  const neverAskDeleteDialog = localStorage.getItem('never-ask-delete-dialog')
  
  const { setControls, controls } = useApropoStore()
  const { useControlByIdQuery, useDeleteControlMutation } = useAboutTab(classId!)
  
  const [dialogState, dispatch] = useReducer(
    (state: DialogState, action: Partial<DialogState>) => ({
      ...state,
      ...action
    }),
    initialState
  )

  // Query to get control details
  const { data: previewEval, isLoading } = useControlByIdQuery(evaluation?.id)

  const deleteControlMutation = useDeleteControlMutation()

  const updateEvaluation = () => {
    navigate(`${location.pathname}/create-evaluation-next?evalId=${evaluation?.id}`)
  }

  const plannedEvaluation = () => {
    // Set selected control in scheduling store if available
    // setSelectedControl(evaluation) // You might need to implement this
    navigate(`${location.pathname}/schedule-assessment`)
  }

  const onDelete = async () => {
    try {
      // If user has "never ask" enabled and evaluation is not assigned, delete immediately
      if (
        neverAskDeleteDialog === 'true' &&
        !isAnyEvaluationAssigned(evaluation?.id, controls)
      ) {
        await deleteControlMutation.mutateAsync(evaluation.id!)

        // Update controls in store
        setControls(
          controls.filter(control => control.id !== evaluation?.id)
        )
        
        console.log('Suppression effectuée')
        return
      }

      // If user hasn't set "never ask" or it's false, and evaluation is not assigned, show confirm dialog
      if (
        (!neverAskDeleteDialog || neverAskDeleteDialog === 'false') &&
        !isAnyEvaluationAssigned(evaluation?.id, controls)
      ) {
        dispatch({ deleteDialogOpen: true })
      } 
      // If evaluation is assigned, show alert that it cannot be deleted
      else if (isAnyEvaluationAssigned(evaluation?.id, controls)) {
        dispatch({ evaluationAlertOpen: true })
      }
    } catch (error) {
      console.error('unexpected error', error)
    }
  }

  const handleConfirmDelete = async () => {
    try {
      await deleteControlMutation.mutateAsync(evaluation.id!)
      setControls(controls.filter(control => control.id !== evaluation?.id))
      dispatch({ deleteDialogOpen: false })
      console.log('Suppression effectuée')
    } catch (error) {
      console.error('Failed to delete evaluation:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="h-4 w-4 rounded-md" />
        <Skeleton className="h-5 w-24 rounded-md" />
      </div>
    )
  }

  return (
    <>
      <EvaluationAlert
        title={t('alert.title', { ns: 'teacher/my-class/apropo' })}
        description={t('alert.description', { ns: 'teacher/my-class/apropo' })}
        cancelLabel={t('alert.ok', { ns: 'teacher/my-class/apropo' })}
        evaluationAlertOpen={dialogState.evaluationAlertOpen}
        dispatch={dispatch}
      />
      
      <ConfirmDialog
        deleteDialogOpen={dialogState.deleteDialogOpen}
        dispatch={dispatch}
        onConfirm={handleConfirmDelete}
        evaluation={evaluation}
      />
      
      {/* Only show update button if there are no submissions */}
      {previewEval?.submissions?.length === 0 && (
        <Button
          variant="ghost"
          className="p-0 flex gap-2 justify-start"
          onClick={updateEvaluation}
        >
          <SquarePen />
          {t('eval_card.update', { ns: 'teacher/my-class/apropo' })}
        </Button>
      )}
      
      <EvaluationElementPreview 
        id={evaluation?.id} 
        loading={isLoading} 
      />
      
      {/* Only show schedule button if evaluation is not assigned */}
      {evaluation?.status !== 'ASSIGNED' && (
        <Button
          variant="ghost"
          className="p-0 flex gap-2 justify-start"
          onClick={plannedEvaluation}
        >
          <CalendarPlus2 />
          {t('eval_card.program', { ns: 'teacher/my-class/apropo' })}
        </Button>
      )}
      
      {/* Delete button - always visible */}
      <Button
        variant="ghost"
        className="p-0 flex gap-2 justify-start text-dinoBotRed hover:text-dinoBotRed/80"
        onClick={onDelete}
      >
        <Trash2 />
        {t('eval_card.delete', { ns: 'teacher/my-class/apropo' })}
      </Button>
    </>
  )
}

export default EvaluationPopover