import React from 'react'
import {
    Dialog,
    DialogContent,
    Di<PERSON>Header,
    DialogTitle,
    DialogClose
} from '@dinobot/components-ui'
import { X } from 'lucide-react' // For close icon

type ImagePreviewModalProps = {
    isOpen: boolean
    onClose: () => void
    imageUrl?: string
    imageName?: string // Optional: for title or alt text
}

export function ImagePreviewModal({
    isOpen,
    onClose,
    imageUrl,
    imageName = 'Image Preview'
}: ImagePreviewModalProps) {
    if (!isOpen || !imageUrl) {
        return null
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[80vw] md:max-w-[70vw] lg:max-w-[60vw] xl:max-w-[50vw] p-0">
                <DialogHeader className="p-4 border-b flex flex-row justify-between items-center">
                    <DialogTitle className="truncate pr-8">
                        {imageName}
                    </DialogTitle>
                </DialogHeader>
                <div className="p-4 max-h-[80vh] overflow-auto flex justify-center items-center">
                    <img
                        src={imageUrl}
                        alt={imageName}
                        className="max-w-full max-h-[calc(80vh-100px)] object-contain rounded-md"
                    />
                </div>
            </DialogContent>
        </Dialog>
    )
}
