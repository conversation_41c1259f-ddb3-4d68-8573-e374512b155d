import { cn } from '@dinobot/utils'
import React from 'react'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypemathjax from 'rehype-mathjax'
import rehypeRaw from 'rehype-raw'
import { MemoizedReactMarkdown } from '@dinobot/components-ui'

interface QuestionViewerProps {
    value: string
}

function QuestionViewer({ value }: QuestionViewerProps) {
    return (
        <MemoizedReactMarkdown
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeRaw, rehypemathjax]}
            components={{
                p({ children }) {
                    return (
                        <p
                            className={cn(
                                'prose-red break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0',
                                'prose-p:mt-1',
                                'mb-2 last:mb-0'
                            )}
                        >
                            {children}
                        </p>
                    )
                }
            }}
        >
            {value || ''}
        </MemoizedReactMarkdown>
    )
}

export default QuestionViewer
