import Cookies from 'js-cookie';
import { Session } from '@dinobot/utils'
import { BotMessage } from './chat-components';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

export function EmptyScreen({ session }: { session: Session }) {
    const {t} = useTranslation(['app/chat'],{keyPrefix:'emptyScreen'})

    //const topic = cookies.get('topic')

    const feature = Cookies.get('feature')

    return (
        <div className="flex flex-col items-center h-[200px]">
            <div className="flex flex-row subfhd:flex-col items-center mx-auto max-w-2xl px-4 mb-6">
                <img
                    src='/dinobot-logo.svg'
                    alt="DinoBot Logo"
                    className={`w-24 subfhd:w-44 2xl:w-60 mr-10 mb-4 animate-fade-in-down `}
                />
                <div className="flex flex-col gap-2 rounded-2xl border border-dinoBotBlue bg-background p-4 animate-fade-in-left">
                    <h1 className="text-lg subfhd:text-sm  font-semibold text-dinoBotBlue">
                        {t('h1')}
                    </h1>
                    <p className="text-xs subfhd:md:text-base leading-normal text-muted-foreground mb-2">
                        {/*             {topic === "Mathématiques" ? "Votre plateforme pour dompter les Mathématiques ! Je suis là pour décortiquer tous les concepts et les exercices qui vous donnent du fil à retordre. Ensemble, on va transformer les équations en récréations !" 
            : topic === "Physique" ? "Votre plateforme pour dompter la Physique ! Je suis là pour décortiquer tous les concepts et les exercices qui vous donnent du fil à retordre. Ensemble, on va transformer les équations en récréations !"
            : topic === "Chimie" ? "Votre plateforme pour dompter la Chimie ! Je suis là pour décortiquer tous les concepts et les exercices qui vous donnent du fil à retordre. Ensemble, on va transformer les réactions en récréations !"
            : "Votre plateforme pour dompter les sciences de la vie et de la terre ! Je suis là pour décortiquer tous les concepts et les exercices qui vous donnent du fil à retordre. Ensemble, on va transformer les expérimentations en récréations !"
            } */}
                        {t('p')}
                    </p>

                    {session?.user ? (
                        <div>
                            {feature === 'Chat' ? (
                                <div className="text-xs subfhd:text-base leading-normal text-dinoBotBlue font-bold  animate-fade-in-up">
                                    {t('chat')}
                                </div>
                            ) : feature === 'Exam' ? (
                                <div className="text-xs subfhd:text-base leading-normal text-dinoBotBlue font-bold  animate-fade-in-up">
                                    {t('exam')}
                                </div>
                            ) : (
                                <div className="text-xs subfhd:text-base leading-normal text-dinoBotBlue font-bold  animate-fade-in-up">
                                    {t('else')}
                                </div>
                            )}
                        </div>
                    ) : (
                        <div className="flex flex-col items-center gap-2 ml-2 mt-2">
                            <BotMessage content={t('is-connect')} />
                            <Link
                                to="/login"
                                className="px-2 mb-1 h-8 text-center bg-dinoBotBlue border border-dinoBotBlue rounded-2xl text-white hover:bg-white hover:text-dinoBotBlue sm:ml-2 transition-all duration-300 "
                            >
                                {t('connexion')}
                            </Link>
                        </div>
                    )}

                    {/*            <MemoizedReactMarkdown
            className="prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0"
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              p({ children }) {
                return <p className="mb-2 last:mb-0 ">{children}</p>
              },
            }}
          >
            {`$\\begin{array}{|c|c|c|c|c|}\n\\hline \\begin{array}{c}\n\\text { Nom de la solution } \\\\\n\\text { aqueuse }\n\\end{array} & \\begin{array}{c}\n\\text { Nom des ions + } \\\\\n\\text { (CATIONS) }\n\\end{array} & \\begin{array}{c}\n\\text { Nom des ions - } \\\\\n\\text { (ANIONS) }\n\\end{array} & \\begin{array}{c}\n\\text { Formule de la } \\\\\n\\text { solution }\n\\end{array} & \\begin{array}{c}\n\\text { Formule du soluté } \\\\\n\\text { dissous }\n\\end{array} \\\\\n\\hline \\text { sulfate de cuivre II } & & & & \\\\\n\\hline \\text { chlorure de cuivre II } & & & & \\\\\n\\hline \\text { sulfate de fer II } & & & & \\\\\n\\hline \\text { chlorure de fer III } & & & & \\\\\n\\hline \\text { Sulfate de ferIII } & & & & \\\\\n\\hline \\text { sulfate de zinc II } & & & & \\\\\n\\hline \\text { chlorure de sodium I } & & & & \\\\\n\\hline \\text { Chlorure de zinc II } & & & & \\\\\n\\hline\n\\end{array}$`}
          </MemoizedReactMarkdown>  */}
                </div>
            </div>
        </div>
    )
}
