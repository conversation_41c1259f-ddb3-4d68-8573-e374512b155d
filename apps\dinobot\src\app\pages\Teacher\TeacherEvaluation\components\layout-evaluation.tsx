import { Link } from 'react-router-dom'
import { cn } from '@dinobot/utils'
import { ArrowLeft } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import React, { ReactNode } from 'react'
import { getLangDir } from 'rtl-detect'

type LayoutProps = {
    children: ReactNode
    title?: ReactNode
    classId: string
}

const LayoutEvaluation = ({
    children,
    title = "Création d'évaluation",
    classId
}: LayoutProps) => {
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)

    return (
        <div className="size-full overflow-hidden flex flex-col pt-11 pb-4 gap-8" dir={dir}>
            <div className="w-full flex flex-col gap-4 px-32">
                <div className="flex gap-3 items-center">
                    <Link
                        to={`/my-classes/${classId}`}
                        className="size-12 flex justify-center items-center border-2 border-dinoBotLightGray rounded-md hover:bg-dinoBotBlue/20"
                    >
                        <ArrowLeft
                            className={cn(dir === 'rtl' && 'rotate-180')}
                        />
                    </Link>
                    <div className="text-4xl font-bold text-dinoBotGray/90">
                        {title}
                    </div>
                </div>
            </div>
            <div className="w-full grow overflow-y-auto px-10 2xl:px-24">
                {children}
            </div>
        </div>
    )
}

export default LayoutEvaluation
