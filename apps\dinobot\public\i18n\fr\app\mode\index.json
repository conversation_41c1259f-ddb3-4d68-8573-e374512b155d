{"controle": {"timer": "Temps restant", "answerControl": {"successMessage": "Vos réponses ont été soumises avec succès, veuillez vérifier votre copie", "errorMessage": "Une erreur s'est produite lors de la soumission de vos réponses", "saveAllResponsePlease": "Veuillez enregistrer toutes vos réponses avant de soumettre", "placeholder": "Votre réponse...", "addMathFormula": "Ajouter une formule mathématique", "editAnswer": "Modifier la réponse", "saveAnswer": "Enregistrer la réponse", "submitAnswer": "Soumettre mes réponses"}, "controlPreview": {"controlSubject": "Sujet du contrôle", "myCopy": "Ma copie", "correction": "Correction", "controlOf": "Contrôle de", "noAnswer": "Pas de réponse.", "copyExercise": "Copier l'exercice", "printExercise": "Imprimer l'exercice", "noControle": "pas de controle", "errorOccurred": "<PERSON><PERSON><PERSON><PERSON>, une erreur est survenue lors de la préparation de l’examen.", "imageNotFound": "Désolé, nous n’avons pas pu afficher l’image.", "failedToDownload": "Désolé, nous n’avons pas pu télécharger le fichier de l’exercice."}, "prof_review": {"title": "Résumé du ", "name": "Nom et prénom de l'élève", "date": "Date de l'évaluation", "du": "<PERSON>", "au": "Au", "type": "Type de l'évaluation", "note": "Note obtenu"}}, "create-controle": {"title": "<PERSON><PERSON>er un Contrôle", "chapter": {"title": "Chapitre", "placeholder": "Sélectionner un chapitre"}, "duration": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "tinfo": "Veuillez sélectionner un chapitre et définir une durée", "loading": "Chargement...", "error": {"fetchChapters": "Erreur lors du chargement des chapitres", "fetchParts": "Erreur lors du chargement des parties", "validation": "Veuillez vérifier vos sélections"}}, "exo": {"level": {"title": "Choisis une classe"}, "module": {"chois": "<PERSON><PERSON> Mat<PERSON>", "math": "Mathématiques", "fr": "Français", "physique-chimie": "Physique-Chimie", "svt": "SVT", "emc": "EMC", "geo": "Géographie", "history": "Histoire"}, "tab": {"title": "Générer un exercice", "secondTitle": "Comment souhaitez-vous créer ?", "db": {"title": "Base de données", "tinfo": {"n10": "Le nombre de questions ne peut pas être supérieur à 10 !", "n1": "Le nombre de questions ne peut pas être inférieur à 1 !", "info": "Vous devrez remplir les champs obligatoire !", "error": "Le nombre de questions doit être compris entre 1 et 10 !"}, "subject": {"name": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON>", "error-chois": "Vous devrez choisir une Matière"}, "chois": {"title": "<PERSON>sir un Chapitre", "placeholder": "<PERSON>sir un Chapitre"}, "error-chois": "Vous devrez choisir un chapitre", "chois-partie": "Choisir une partie", "error-chois-partie": "Vous devrez choisir une partie", "part": {"title": "<PERSON><PERSON>", "info": "Choisir une partie", "error": "Vous devrez choisir une partie", "placeholder": "Choisir une partie"}, "questions-lenght": "Nombre d'exercice", "exonumber": "Nombre d'exercices", "questions-number": "Nombre de questions", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom-prompt": "Personnaliser avec un prompt", "opt": "(optionnel)", "detail": "Explique tes besoins à DinoBot, il te génère un exercice sur mesure.", "oublie": "N’oublie pas : ta demande doit être claire et précise.", "redigez": "Rédigez ici...", "caracteres": "caractères", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "file": {"title": "À partir d’un fichier", "error": "Vous devrez charger un énoncé au format PDF !", "generate": "Générer à partir d’un fichier", "chois": "Choisir un chapitre", "download": "Télécharge ton fichier dans DinoBot, il en extrait le contenu pour créer des exercices adaptés.", "rappelle": "Rappelle-toi : ton fichier doit être lisible et pertinent pour le sujet d'étude.", "enonce": "Énon<PERSON>", "Remplacez le fichier": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "Téléchargez un fichier": "Téléchargez un fichier", "correction": "Corrigé (optionnel)", "max": "le fichier doit être au format .pdf, 100 Mo maximum", "replace": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "upload": "Téléchargez un fichier", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prompt": "Personnaliser avec un prompt", "opt": "(optionnel)", "exp": "Explique tes besoins à DinoBot, il te génère un exercice sur mesure.", "noubliep": "N’oublie pas : ta demande doit être claire et précise.", "rid": "Rédigez ici...", "caracteres": "caractères", "questions": "Vous ne pouvez pas générer plus de 10 questions.", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "exam": {"title": "À partir d’un examen", "toast": {"n10": "Le nombre de questions ne peut pas être supérieur à 10 !", "n1": "Le nombre de questions ne peut pas être inférieur à 1 !", "n1&10": "Le nombre de questions doit être compris entre 1 et 10 !", "fill": "Vous devrez remplir les champs obligatoire !"}, "table": {"title": "Intitulé", "nofill": "Non renseigné", "year": "<PERSON><PERSON>"}, "chapitre": "Chapitre", "chois": "Choisir un chapitre", "chois-error": "Vous devrez choisir un chapitre", "exam": "Examen", "chois-savoir": "Choisir un savoir", "error-chois-savoir": "Vous devrez choisir un savoir", "chois-savoir-text": "Choisissez un savoir proposé par DinoBot et obtenez des exercices d'entraînement personnalisés !", "result": "résultats correspondant à vos critères", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "have": "Vous ne pouvez pas générer plus de 10 questions.", "prompt": "Personnaliser avec un prompt", "opt": "(optionnel)", "besoins": "Explique tes besoins à DinoBot, il te génère un exercice sur mesure.", "noubliep": "N’oublie pas : ta demande doit être claire et précise.", "rid": "Rédigez ici...", "caracteres": "caractères", "submit": "<PERSON><PERSON><PERSON><PERSON>"}}, "mini-tabs": {"file": {"generate": "Générer à partir d’un fichier", "chois-chap": "Choisir un chapitre", "download": "Télécharge ton fichier dans DinoBot, il en extrait le contenu pour créer des exercices adaptés.", "rappelle": "Rappelle-toi : ton fichier doit être lisible et pertinent pour le sujet d'étude.", "enonce": "Énon<PERSON>", "remp-file": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "down-file": "Téléchargez un fichier", "correction": "Corrigé (optionnel)", "max": "le fichier doit être au format .pdf, 100 Mo maximum", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "error": "Vous devrez charger un énoncé au format PDF !"}, "db": {"chois": "Choisir un chapitre", "chap": "Chapitre", "part": "<PERSON><PERSON>", "error-chois": "Vous devrez choisir un chapitre", "chois-part": "Choisir une partie", "error-chois-part": "Vous devrez choisir une partie", "nbr": "Nombre d'exercices", "exonumber": "Nombre d'exercices", "questions-number": "Nombre de questions", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n10": "Le nombre de questions ne peut pas être supérieur à 10 !", "n1": "Le nombre de questions ne peut pas être inférieur à 1 !", "n1&10": "Le nombre de questions doit être compris entre 1 et 10 !", "error": "Vous devrez remplir les champs obligatoire !", "submit": "<PERSON><PERSON><PERSON><PERSON>"}, "exam": {"chap": "Chapitre", "chois-chap": "Choisir un chapitre", "error-choi-chap": "Vous devrez choisir un chapitre", "exam": "Examen", "chois-sav": "Choisir un savoir", "error-chois-sav": "Vous devrez choisir un savoir", "chois-sav-prps": "Choisissez un savoir proposé par DinoBot et obtenez des exercices d'entraînement personnalisés !", "res": "résultats correspondant à vos critères", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "np10": "Vous ne pouvez pas générer plus de 10 questions.", "n10": "Le nombre de questions ne peut pas être supérieur à 10 !", "n1": "Le nombre de questions ne peut pas être inférieur à 1 !", "n1&10": "Le nombre de questions doit être compris entre 1 et 10 !", "error": "Vous devrez remplir les champs obligatoire !", "submit": "<PERSON><PERSON><PERSON><PERSON>", "table": {"vide": "Aucun exercice trouvé.", "no-write": "Non renseigné"}}}}, "train": {"exo": "Exercice", "copyexo": "Copier l'exercice", "regexo": "Régénérer l'exercice", "question": "Question", "printexo": "Imprimer l'exercice", "creation": "Votre exercice est en cours de création...", "continue": "Pour continuer à discuter ensemble, connecte-toi !", "env": {"part1": "La variable d'environnement est manquante !", "part2": "est manquante !"}, "error": "Une erreur inattendue s'est produite lors de la génération de l'exercice", "beta": {"message": "Vous découvrez actuellement une fonctionnalité en mode bêta ", "description": "Vos retours sont précieux pour nous aider à l’améliorer. Merci pour votre aide et votre contribution ! 🙏"}}}