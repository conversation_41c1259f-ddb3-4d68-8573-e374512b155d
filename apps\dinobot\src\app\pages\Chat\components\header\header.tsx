//Hooks
import * as React from 'react'


//Components
import { UserMenu } from './user-menu'

//Logo

//Icons
import { LogIn } from 'lucide-react'

import Cookies from 'js-cookie'


import { useTranslation } from 'react-i18next'
import { Session } from '@dinobot/utils'
import { User } from '@dinobot/prisma'
//TODO--------------
// import { getFeaturesFlagsByNames } from '@/lib/features/actions'
// import { FeatureFlagName } from '@prisma/client'
import SplitScreen from './split-screen'
import { Button, ChatHistory, FilesSidebarMobile, FilesSidebarToggle, Select, SelectIntl, SelectTrigger, SelectValue, SidebarMobile, SidebarToggle } from '@dinobot/components-ui'
import { SelectTopic } from './select/select-topic'
import SwitchAvatar from '@dinobot/components-ui/lib/avatar/components/switch-avatar'
import { FileHistory } from '@dinobot/components-ui/lib/files/file-history'
import DisconnectButton from '../../../Auth/login/components/disconnect'
import { Link } from 'react-router-dom'


function UserSection() {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    return (
        <UserMenu user={session.user} />
    )
}
// TODO decoment
// function getFeatureFlagByName(
//     featureName: string[],
//     features: { featureName: FeatureFlagName; isEnabled: boolean }[]
// ) {
//     return features.find(feature => featureName.includes(feature.featureName))
//         ?.isEnabled
// }

function UserOrLogin({
    isChatModeEnabled
}: {
    isChatModeEnabled: boolean
}) {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const {t,i18n} = useTranslation(['app/headers'],{keyPrefix:"login"})
    const locale = i18n.language 
    return (
        <div
            className={`flex flex-row justify-center items-center ${locale === 'ar' ? 'gap-4' : ''}`}
        >
            {session?.user ? (
                <div>
                    {isChatModeEnabled ? (
                        <>
                            <SidebarMobile>
                                <ChatHistory userId={session.user.id} />
                            </SidebarMobile>
                            <SidebarToggle />
                        </>
                    ) : null}
                </div>
            ) : (
                <Link to="/" rel="nofollow">
                    <img
                        src='/dinobot-logo-small.svg'
                        alt="DinoBot Logo"
                        className="min-w-[40px] w-[40px] sm:mr-2"
                    />
                </Link>
            )}
            <div className="flex items-center ">
                {/* <IconSeparator className="size-6 text-muted-foreground/50" /> */}
                {session?.user ? (
                    <UserSection />
                ) : (
                    <Button
                        asChild
                        className="px-2 mb-1 bg-transparent border border-gray-500 rounded-2xl text-gray-500 hover:bg-gray-500 hover:text-white sm:ml-2 transition-all duration-300 "
                    >
                        <Link to="/login">
                            <LogIn className="block sm:hidden size-4" />
                            <span className="hidden sm:block">
                                {t('title')}
                            </span>
                        </Link>
                    </Button>
                )}
            </div>
        </div>
    )
}

function FilesSideBar() {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session

    return (
        <div>
            {session?.user ? (
                <>
                    <FilesSidebarMobile>
                        <FileHistory userId={session.user.id} />
                    </FilesSidebarMobile>
                    <FilesSidebarToggle />
                </>
            ) : null}
        </div>
    )
}

export function Header({ loginType = true }: { loginType?: boolean }) {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const user = session?.user as User
    const avatarModeEnabled = import.meta.env.APP_AVATAR_MODE === 'true' || false
    // const chatModeExamModeFlag = (await getFeaturesFlagsByNames([
    //     FeatureFlagName.STUDENT_CHAT_MODE,
    //     FeatureFlagName.STUDENT_EXAM_MODE
    // ])) as { featureName: FeatureFlagName; isEnabled: boolean }[]

    return (
        <header className="sticky top-0 z-10 flex items-center h-16 md:h-16 px-4 border-b shrink-0 bg-gradient-to-b from-background/10 via-background/50 to-background/80 backdrop-blur-xl md:grid md:grid-cols-8 shadow-lg shadow-dinoBotCyan/15 flex-row justify-between">
            <div className="sm:w-fit flex items-center col-span-2">
                {loginType && (
                    <React.Suspense
                        fallback={<div className="flex-1 overflow-overlay" />}
                    >
                        <UserOrLogin
                            isChatModeEnabled={
                                // getFeatureFlagByName(
                                //     [
                                //         FeatureFlagName.STUDENT_CHAT_MODE,
                                //         FeatureFlagName.STUDENT_EXAM_MODE
                                //     ],
                                //     chatModeExamModeFlag
                                // )!
                                true
                            }
                        />
                    </React.Suspense>
                )}
                {!loginType && (
                    <Select>
                        <SelectTrigger className="w-[240px] [&>svg]:stroke-[3.5] [&>svg]:text-white [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotCyan bg-dinoBotCyan">
                            <SelectValue
                                placeholder={
                                    <div className="flex items-center gap-2">
                                        <img
                                            src='/icon _doc_detail_.svg'
                                            alt="icon doc details"
                                            width={20}
                                            height={20}
                                            className="w-5 h-5"
                                        />
                                        <span>mode exercise</span>
                                    </div>
                                }
                            />
                        </SelectTrigger>
                    </Select>
                )}
            </div>
            <div
                className={`w-fit gap-2 md:h-auto flex flex-col md:flex-row justify-start md:justify-center  items-center md:items-start col-span-3 col-start-3 ${session?.user ? '' : 'ml-2'}`}
            >
                {loginType && <SelectTopic user={user!} />}
            </div>

            <div className="flex flex-row justify-between items-center col-span-3 col-start-6">
                <div className="sm:w-fit lg:h-auto flex flex-col justify-center items-end">
                    {/* {getFeatureFlagByName(
                        [
                            FeatureFlagName.STUDENT_CHAT_MODE,
                            FeatureFlagName.STUDENT_EXAM_MODE
                        ],
                        chatModeExamModeFlag
                    ) ? ( */}
                        <React.Suspense
                            fallback={
                                <div className="flex-1 overflow-overlay" />
                            }
                        >
                            <FilesSideBar />
                        </React.Suspense>
                    {/* ) : null} */}
                </div>
                {loginType &&
                    (avatarModeEnabled && session?.user ? (
                        <SwitchAvatar />
                    ) : null)}
                <SplitScreen />
                <SelectIntl />
                {session?.user ? (
                    <form
                        //className='flex flex-col justify-center items-end'
                        action={async () => {
                            'use server'
                            // await signOut()
                            // revalidatePath('/')
                        }}
                    >
                        <DisconnectButton />
                    </form>
                ) : null}
            </div>
        </header>
    )
}