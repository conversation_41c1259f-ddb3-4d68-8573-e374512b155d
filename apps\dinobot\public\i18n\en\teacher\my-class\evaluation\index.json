{"params": {"title": "Evaluation Parameters", "formative": "Formative", "diagnostic": "Diagnostic", "summative": "Summative", "certifying": "Certifying", "total": "Fixed total", "form_name": "Evaluation name:", "form_name_placeholder": "Name of the new evaluation...", "form_name_error": "Evaluation name is required", "form_class": "Class:", "form_domain": "Subject:", "form_domain_placeholder": "Select a subject", "form_domain_error": "Please select a subject", "form_notation": "Notation:", "form_notation_placeholder": "Select a notation", "form_notation_error": "Please select a notation", "form_type": "Type:", "form_type_placeholder": "Choose a type of evaluation", "form_type_error": "Please select a type of evaluation", "form_theme": "Theme:", "form_theme_placeholder": "Select a theme", "form_theme_error": "Please select a theme or create one", "form_new_theme": "New theme", "form_new_theme_placeholder": "Enter the name of the new theme", "form_param_view": "Visual parameters", "form_police": "Font:", "form_police_placeholder": "Select a font", "form_police_error": "Please select a font", "form_size": "Font size:", "form_size_placeholder": "Choose the size", "form_size_error": "Please select a font size", "submit_loading": "Processing", "submit_next": "Next", "submit_update": "Update", "submite_message": "Changes have been saved successfully", "error_create_theme": "Error creating the theme", "error_theme": "Error retrieving themes"}, "next": {"title": "Creating exercises", "preview": "Preview", "prv_eval": "the evaluation", "add_exo": "Add an exercise and its solution", "submit": {"loading": "Processing", "save_br": "Save draft", "update_br": "Update draft", "create": "Create evaluation", "update": "Update evaluation", "shedule": "Schedule the evaluation"}, "exo": {"title": "Exercise", "dependant": "Dependent", "independent": "Independent", "descript": "Dependent: have a direct link with the information and answers of other questions", "add": "add a question", "statement": {"title": "Generate a contextual statement", "question_title": "Additional instructions for AI", "question_title_result": "Statement", "placeholder": "Write here..."}, "question": {"title": "Question", "type": "Question type", "type_placeholder": "Select a type", "notation": "Question notation", "question_title": "Statement", "placeholder": "Write here...", "solution": "Expected answer", "generate": "Generate with AI", "quizz": "quiz", "question_de_connaissances": "knowledge question", "question_calculatoire": "calculation question", "question_d_analyses": "analysis question", "question_synthese": "synthesis question", "question_reflexion_critique": "critical thinking question"}, "select": {"title": "Question", "bac": "bac", "brevet": "brevet", "custom": "Customize"}}, "titles": {"create": "Creating an evaluation", "class": "Class", "update": "Updating an evaluation"}}, "mediaDialog": {"addAttachment": "Add an attachment", "import": "Import", "importImage": "Import an image", "importVideo": "Import a video", "cancel": "Cancel", "save": "Save", "fileTooLarge": "The file {fileName} is too large. You cannot upload a file larger than {maxSize}MB", "invalidFileType": "The file format {fileName} is not accepted", "maxAttachmentsReached": "You have reached the maximum number of attachments ({maxCount})", "fileTooLargeSimple": "The file is too large", "croppingError": "Error while cropping the image"}, "mediaTooltip": {"acceptedFormats": "Accepted Formats", "images": "Images:", "videos": "Videos:", "maxSize": "Maximum allowed size:", "sizeLimit": "{size} MB per file", "ensureCompliance": "Make sure your file meets these requirements before uploading."}, "dialog": {"params": {"title": "Evaluation Parameters", "param": "Parameters", "prm_eval": "the evaluation"}, "db": {"btn_title": "Database", "title": "Database", "add": "Add the question", "answer": "Answer", "loading": "Processing", "exo": "Exercise", "add_exo": "Add the exercise", "no_question": "No question found."}, "exam": {"subject": "Subjects of", "loading": "Processing", "chois": "<PERSON><PERSON>", "name": "Exam name", "year": "Year", "no_exam": "No exam found."}, "file": {"import": "Import", "import_exo": "Import an exercise", "question_file": "{iscours, select, cours {Course} exercice {Statement} other {Statement}}", "exo": "Exercise", "course": "Course", "file_max": "The file must be in .pdf, PNG or JPG format. Maximum 25 MB", "solution_file": "Solution", "replace": "Replace the file", "upload": "Upload a file", "loading": "Processing", "add": "Add", "error": "An error occurred!"}}, "schedule": {"title": "Schedule an evaluation", "footer_title": "The concerned students will be notified once the evaluation is scheduled", "settings": {"title": "Settings", "lable_no": "Not timed", "lable_yes": "Timed", "no_learn": "No training", "autorised_learn": "Training allowed"}, "form": {"name": "Evaluation name", "name_placeholder": "Name of the new evaluation...", "name_erro": "Please enter a title", "descript": "Evaluation description", "descript_placeholder": "Description of the evaluation...", "subject": "Subject and solution", "class": "Concerned classes", "submit": "Schedule", "loading": "Processing...", "create_at": "Created on", "date_error": "The due date must be later than the available date", "time_limit_error": "Please set a duration for the timed evaluation", "past_date_error": "The available date cannot be in the past"}, "date": {"title": "Due date", "dispo": "Available from", "dispo_erro": "Date required", "dispo_placeholder": "Event date", "due": "Until", "due_erro": "Date required", "due_placeholder": "Event date"}}, "preview": {"title": "Evaluation Preview", "exo": "Exercise", "domain": "Subject", "noAnswer": "No answer.", "name": "Evaluation name", "level": "Level"}}