//Componets
import { Sidebar } from './sidebar'

//---------TODO---------

//Hooks
import Cookies from 'js-cookie'


//Translations
import { getLangDir } from 'rtl-detect'
import { useTranslation } from 'react-i18next'
import { ChatHistory } from '../chat'

export function SidebarDesktop() {
    const session = JSON.parse(Cookies.get('session') || '{}')
    const {i18n} =useTranslation()
    if (!session?.user?.id) {
        return null
    }
    const direction = getLangDir(i18n.language)
    return (
        <Sidebar
            className={`peer absolute inset-y-0 z-30 hidden  shadow-[-6px_-6px_20px_2px_rgba(0,0,0,0.1),_6px_6px_20px_2px_rgba(45,78,255,0.15)] bg-white duration-300 ease-in-out ${direction === 'ltr' ? '-translate-x-full rounded-tr-3xl data-[state=open]:translate-x-0' : 'translate-x-full rounded-tl-3xl data-[state=open]:translate-x-0'} lg:flex lg:w-[250px] xl:w-[300px] mt-4`}
        >
            <ChatHistory userId={session.user.id} />
        </Sidebar>
    )
}
