import React from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { Button } from '@dinobot/components-ui'
import { IconCalendar } from '@dinobot/components-ui'
import { Class } from '../my-classes.types'
import { cn } from '@dinobot/utils'

interface ClassComponentProps {
  className?: string
  classe: Class
}

const ClassComponent: React.FC<ClassComponentProps> = ({ className, classe }) => {
  const { t } = useTranslation('teacher/my-class/classes')
  const navigate = useNavigate()

  const handleClick = () => {
    navigate(`/my-classes/${classe.id}`)
  }

  const goToCalendar = () => {
    navigate(`/my-classes/${classe.id}?tab=about`)
  }

  return (
    <div
      className={cn(
        'rounded-lg w-96 h-16 flex justify-evenly items-center font-semibold',
        className
      )}
      style={{ backgroundColor: classe.classColor! }}
    >
      <div
        className="w-2/3 flex justify-between items-center cursor-pointer"
        onClick={handleClick}
      >
        <div className="rounded-sm px-3 py-1 bg-white text-[100%] min-w-20 w-fit h-12 flex items-center justify-center text-center">
          {classe.name}
        </div>
        <p className="text-blue-950 italic ml-1">
          {classe.students.length} {t('class_eleves')}
        </p>
        <span></span>
      </div>
      <Button variant="link" onClick={goToCalendar}>
        <IconCalendar />
      </Button>
    </div>
  )
}

export default ClassComponent