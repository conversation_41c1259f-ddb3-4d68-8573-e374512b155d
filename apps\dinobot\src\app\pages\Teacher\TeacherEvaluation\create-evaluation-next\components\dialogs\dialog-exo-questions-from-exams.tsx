import { Button } from '@dinobot/components-ui'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@dinobot/components-ui'
import { Loader2 } from 'lucide-react'
import React, { useEffect, useState, useTransition } from 'react'
import QuestionsFromExamTable from './components/questions-from-exam-Table'
import { ExamPdfViewer } from './components/exam-pdf-preview'
import { useAuthApiClient } from '../../../../../../contexts/AppContext'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useEvaluationParamsStore } from '../../../store/evaluation-params.store'
import { JsonValue } from '@prisma/client/runtime/library'
import { useTranslation } from 'react-i18next'
import { ExoOutput, ExoSortsAndFilters } from '../../../teacherEvaluation.types'

type DialogExoQuestionsFromExamsProps = {
    title: string
    exoIndex: number
    quesNum: number
}

const DialogExoQuestionsFromExams = ({
    title,
    exoIndex,
    quesNum
}: DialogExoQuestionsFromExamsProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/exam')
    const [open, setOpen] = useState(false)
    const [isAdd, onAdd] = useTransition()
    const [pdf, setPDF] = useState<any>(null)
    const [exoId, setExoId] = useState<string | null>(null)
    const [exams, setExams] = useState<ExoOutput[]>([])
    const { addQuestions, domain, level } = useEvaluationParamsStore()

    const openAssignementPDF = async () => {
        if (exoId) {
            const exo = await getExercise(exoId)
            setPDF({
                name: `${exo.title} - énoncé`,
                type: 'pdf',
                data: `data:application/pdf;base64,${exo.assignmentMedia}`
            })
        }
    }
    const handleSortFilters = async (value: ExoSortsAndFilters) => {
        if (value) {
            value = {
                ...value,
                filters: {
                    ...value.filters,
                    domainId: domain?.id,
                    levelId: level?.id
                }
            }
            const newExos = (await getSortedFilteredExams(value)) as ExoOutput[]
            setExams(newExos)
        }
    }
    const handleAddExo = () => {
        onAdd(async () => {
            const ques = await generateControle({ exerciseId: exoId! })
            const questions = ques?.at(0)?.map(que => ({
                content: que?.questionContent,
                desmosCode: que.desmosCode as JsonValue,
                solution: que.solution
            }))
            addQuestions(exoIndex, quesNum, [...questions!])
            setOpen(false)
        })
    }
    useEffect(() => {
        // console.log('Exam Id', examId)
        if (exoId) {
            ;(async () => {
                await openAssignementPDF()
            })()
        } else {
            setPDF(null)
        }
    }, [exoId])
    return (
        <Dialog
            open={open}
            onOpenChange={open => {
                setOpen(open)
                setPDF(null)
            }}
        >
            <DialogTrigger className="flex flex-col items-center justify-center hover:no-underline gap-1 h-fit">
                <div className="rounded-full flex justify-center items-center border p-1 border-dinoBotVibrantBlue/30 bg-dinoBotWhite/80">
                    <img
                        src={'/dinobot-head.svg'}
                        width={34}
                        height={34}
                        alt="from db icon"
                    />
                </div>
                <span>{title}</span>
            </DialogTrigger>
            <DialogContent
                className={`flex min-w-fit w-auto ${pdf ? 'max-w-[1400px] 2xl:max-w-[1800px]' : 'max-w-[1200px] 2xl:max-w-[1600px]'} h-[600px] 2xl:h-[800px] transition delay-150 duration-300 ease-in-out`}
            >
                <div className="w-fit">
                    <ExamPdfViewer show={true} pdf={pdf} />
                </div>
                <div className="flex-1">
                    <DialogHeader>
                        <DialogTitle>
                            {t('subject')} {title.toLowerCase()}
                        </DialogTitle>
                        <DialogDescription></DialogDescription>
                    </DialogHeader>
                    <div className="flex h-full">
                        <div className="w-auto h-full">
                            <div className="w-fit h-full">
                                <div className="pr-2 h-5/6 overflow-auto custom-scroller">
                                    <div className=" flex flex-col gap-2 h-full">
                                        <QuestionsFromExamTable
                                            exams={exams}
                                            setExoId={setExoId}
                                            filtersSortChange={
                                                handleSortFilters
                                            }
                                        />
                                    </div>
                                </div>
                                <div className="w-full flex justify-center items-center mt-4">
                                    <Button
                                        onClick={handleAddExo}
                                        className="bg-dinoBotBlue text-white hover:bg-dinoBotBlue/80 hover:text-white"
                                        disabled={isAdd}
                                    >
                                        {isAdd ? (
                                            <>
                                                <Loader2 className="mr-2 size-4 animate-spin" />
                                                {t('loading')}
                                            </>
                                        ) : (
                                            t('chois')
                                        )}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default DialogExoQuestionsFromExams
