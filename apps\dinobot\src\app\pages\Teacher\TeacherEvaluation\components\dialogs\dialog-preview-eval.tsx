import React, { ReactNode, useRef } from 'react'
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@dinobot/components-ui'
import { Download, Printer } from 'lucide-react'
import { ControlExercisePartialWithRelations } from '@dinobot/prisma'
import ControleViewer from './components/controle-viewer'
import { Domain } from '@dinobot/prisma'
import { Level } from '@dinobot/prisma'
import { ScrollArea } from '@dinobot/components-ui'
import { Button } from '@dinobot/components-ui'
import { useReactToPrint } from 'react-to-print'
import generatePDF from 'react-to-pdf'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'

type DialogPreviewEvalProps = {
    exos: ControlExercisePartialWithRelations[]
    domain: Domain
    level: Level
    name: string | undefined
    children: ReactNode
    font: string
    fontSize: number
}

const DialogPreviewEval = ({
    exos,
    domain,
    level,
    name,
    children,
    font,
    fontSize
}: DialogPreviewEvalProps) => {
    const { t, i18n } = useTranslation(['teacher.myClass.evaluation.preview'])
    const dir = getLangDir(i18n.language)
    const ctrlRef = useRef<HTMLDivElement>(null)
    const setCtrlRefSizeToCustomFormat = (format?: string) => {
        if (ctrlRef?.current) {
            switch (format) {
                case 'A4':
                    ctrlRef.current.style.width = '793px'
                    ctrlRef.current.style.height = '1122px'
                    break
                case 'A5':
                    ctrlRef.current.style.width = '594px'
                    ctrlRef.current.style.height = '841px'
                    break
                case 'A6':
                    ctrlRef.current.style.width = '420px'
                    ctrlRef.current.style.height = '594px'
                    break

                default:
                    ctrlRef.current.style.width = '793px'
                    ctrlRef.current.style.height = '1122px'
                    break
            }
        }
    }
    // const {targetRef:ctrlRef,toPDF}=usePDF({filename:`${name}.pdf`})
    // Reset ctrlRef size back to 100%
    const resetCtrlRefSize = () => {
        if (ctrlRef?.current) {
            ctrlRef.current.style.width = '100%'
            ctrlRef.current.style.height = '100%'
        }
    }
    const generatePdfFromCtrlRef = () => {
        // Temporarily set size to A4 for PDF rendering
        setCtrlRefSizeToCustomFormat('A4')

        try {
            // Generate PDF, naming it based on 'data' property
            generatePDF(ctrlRef, {
                filename: `${name}.pdf`
            })
        } finally {
            // Reset size to original dimensions
            resetCtrlRefSize()
        }
    }

    const printCtrl = useReactToPrint({
        contentRef: (() => {
            setCtrlRefSizeToCustomFormat('A4')
            return ctrlRef
        })()
    })

    const handleCtrlPrintClick = () => {
        try {
            printCtrl()
        } finally {
            resetCtrlRefSize()
        }
    }

    return (
        <Dialog>
            <DialogTrigger>{children}</DialogTrigger>
            <DialogContent className="min-w-[700px] h-[90vh]" dir={dir}>
                <DialogHeader className="h-fit flex flex-row justify-between items-center">
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            className="text-sky-950 bg-dinoBotSky/20 size-10 rounded-full p-0"
                            onClick={generatePdfFromCtrlRef}
                        >
                            <Download />
                        </Button>
                        <Button
                            variant="outline"
                            className="text-sky-950 bg-dinoBotSky/20 size-10 rounded-full p-0"
                            onClick={handleCtrlPrintClick}
                        >
                            <Printer />
                        </Button>
                    </div>
                    <DialogTitle>{t('title')}</DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                <ScrollArea className="w-full h-[75vh] ">
                    <ControleViewer
                        exos={exos}
                        domain={domain}
                        level={level}
                        name={name}
                        ref={ctrlRef}
                        font={font}
                        fontSize={fontSize}
                    />
                </ScrollArea>
            </DialogContent>
        </Dialog>
    )
}

export default DialogPreviewEval
