import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import HttpBackend from 'i18next-http-backend'

i18n
  .use(HttpBackend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'en',
    lng: 'fr',

    // Configure backend for your structure
    backend: {
      loadPath: '/i18n/{{lng}}/{{ns}}/index.json',
    },

    // Your namespaces (folder names under app/)
    ns: ['global'],
    defaultNS: 'auth', // or whatever you want as default

    interpolation: {
      escapeValue: false
    }
  })

export default i18n
