import { Context, Next } from 'hono'
import { getCookie } from 'hono/cookie'
import { verifyJWT, getUserById } from './auth'
import { AuthUser, AuthContext } from './types'
import { isUserActive, hasRole, isAdmin } from './utils'
import { logger } from '@/lib/logger'
import { UserTypeSchema } from '@dinobot/prisma'

type UserType = (typeof UserTypeSchema)['_type']

export interface AuthenticatedContext extends Context {
    get: {
        (key: 'jwtPayload'): any
        (key: 'user'): AuthUser | undefined
        (key: 'authContext'): AuthContext | undefined
    }
    set: Context['set']
}

// Extract JWT token from Authorization header or cookie
function extractToken(c: Context): string | null {
    // Try Authorization header first
    const authHeader = c.req.header('Authorization')
    if (authHeader?.startsWith('Bearer ')) {
        return authHeader.substring(7)
    }

    // Fall back to cookie
    const cookieToken = getCookie(c, 'authjs.session-token') || getCookie(c, 'token')
    return cookieToken || null
}

// Core authentication middleware that verifies JWT and sets user context
export async function authenticateJWT(c: Context, next: Next, optional = false): Promise<Response | void> {
    try {
        const token = extractToken(c)

        if (!token) {
            if (optional) {
                c.set('authContext', {
                    user: null,
                    isAuthenticated: false,
                    hasRole: () => false,
                    isAdmin: false
                })
                await next()
                return
            } else {
                return c.json({ error: 'Authentication required' }, 401)
            }
        }

        // Verify JWT token
        const payload = await verifyJWT(token)
        if (!payload) {
            if (optional) {
                c.set('authContext', {
                    user: null,
                    isAuthenticated: false,
                    hasRole: () => false,
                    isAdmin: false
                })
                await next()
                return
            } else {
                return c.json({ error: 'Invalid or expired token' }, 401)
            }
        }

        // Get fresh user data from database
        const user = await getUserById(payload.sub)
        if (!user) {
            if (optional) {
                c.set('authContext', {
                    user: null,
                    isAuthenticated: false,
                    hasRole: () => false,
                    isAdmin: false
                })
                await next()
                return
            } else {
                return c.json({ error: 'User not found' }, 401)
            }
        }

        // Check if user account is still active
        if (!isUserActive(user) && !optional) {
            return c.json({ error: 'Account inactive or email not verified' }, 401)
        }

        // Set authentication context
        c.set('jwtPayload', payload)
        c.set('user', user)
        c.set('authContext', {
            user,
            isAuthenticated: true,
            hasRole: (roles: UserType[]) => hasRole(user, roles),
            isAdmin: isAdmin(user)
        })

        await next()
    } catch (error) {
        logger.error({ error }, 'JWT authentication error')
        if (optional) {
            c.set('authContext', {
                user: null,
                isAuthenticated: false,
                hasRole: () => false,
                isAdmin: false
            })
            await next()
        } else {
            return c.json({ error: 'Authentication failed' }, 401)
        }
    }
}

// Require authentication - user must be logged in
export async function requireAuth(c: Context, next: Next): Promise<Response | void> {
    return authenticateJWT(c, next, false)
}

// Optional authentication - continue even if not logged in
export async function optionalAuth(c: Context, next: Next): Promise<Response | void> {
    return authenticateJWT(c, next, true)
}

// Require specific roles
export function requireRole(allowedRoles: UserType[]) {
    return async (c: Context, next: Next): Promise<Response | void> => {
        const authContext = c.get('authContext') as AuthContext
        if (!authContext?.user) {
            return c.json({ error: 'Authentication required' }, 401)
        }

        if (!authContext.hasRole(allowedRoles)) {
            return c.json({
                error: 'Insufficient permissions',
                required: allowedRoles,
                current: authContext.user.type
            }, 403)
        }

        await next()
    }
}

// Require teacher or admin role
export async function requireTeacher(c: Context, next: Next): Promise<Response | void> {
    const authContext = c.get('authContext') as AuthContext
    if (!authContext?.user) {
        return c.json({ error: 'Authentication required' }, 401)
    }

    if (
      authContext?.user?.type ==
      (UserTypeSchema.Enum.teacher && UserTypeSchema.Enum.establishment)
    ) {
      return c.json({ error: 'Teacher access required' }, 403);
    }

    await next()
}

// Require admin role (establishment type)
export async function requireAdmin(c: Context, next: Next): Promise<Response | void> {
    const authContext = c.get('authContext') as AuthContext
    if (!authContext?.user) {
        return c.json({ error: 'Authentication required' }, 401)
    }

    if (authContext?.user?.type == UserTypeSchema.Enum.establishment) {
      return c.json({ error: 'Admin access required' }, 403);
    }

    await next()
}

// Require student role
export async function requireStudent(c: Context, next: Next): Promise<Response | void> {
    const authContext = c.get('authContext') as AuthContext
    if (!authContext?.user) {
        return c.json({ error: 'Authentication required' }, 401)
    }

    if (authContext?.user?.type == UserTypeSchema.Enum.student) {
      return c.json({ error: 'Student access required' }, 403);
    }

    await next()
}

// Get authenticated user from context
export function getAuthUser(c: Context): AuthUser | null {
    return c.get('user') || null
}

// Get auth context from context
export function getAuthContext(c: Context): AuthContext | null {
    return c.get('authContext') || null
}
