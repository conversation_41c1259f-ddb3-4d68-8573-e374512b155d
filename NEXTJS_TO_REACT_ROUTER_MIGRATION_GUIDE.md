# Next.js to React Router + TanStack Query Migration Guide

This guide provides comprehensive instructions for migrating pages from the old Next.js DinoBot app (`dinobot-old/`) to the new React Router + TanStack Query architecture in the Nx monorepo.

## 🚨 IMPORTANT FOR AI AGENTS

**When working on DinoBot migrations, Claude Code AI agents MUST be aware of:**

1. **Old DinoBot Location**: The original Next.js codebase is located at `dinobot-old/` directory
2. **Reference Source**: Always check `dinobot-old/src/app/` for original implementations when migrating
3. **Migration Status**: Check this file for current migration progress and completed components
4. **Backend APIs**: Verify required APIs exist in `apps/dinobot-backend/src/lib/` before starting frontend migration
5. **Pattern Consistency**: Follow the established patterns from successfully migrated components listed in this guide

## 🎯 Migration Overview

### **Architecture Changes:**
- **From:** Next.js App Router with Server Actions
- **To:** React Router v7 + TanStack Query + Hono.js Backend

### **Key Technology Replacements:**
| Old (Next.js) | New (React Router) | Purpose |
|---------------|-------------------|---------|
| `next-intl` | `react-i18next` | Internationalization |
| `useRouter()` | `useNavigate()` | Navigation |
| `useParams()` from next/navigation | `useParams()` from react-router-dom | URL parameters |
| `useSearchParams()` from next/navigation | `useSearchParams()` from react-router-dom | Query parameters |
| Server Actions | TanStack Query + API hooks | Data fetching |
| `useLocale()` | `useTranslation().i18n.language` | Current language |

## 📋 Step-by-Step Migration Process

### **Step 1: Replace Imports**

```typescript
// OLD - Next.js imports
import { useRouter, useSearchParams, useParams } from 'next/navigation'
import { useLocale, useTranslations } from 'next-intl'
import { someServerAction } from '@/lib/some/actions'

// NEW - React Router imports  
import { useNavigate, useSearchParams, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext'
import { getLangDir } from 'rtl-detect'
```

### **Step 2: Update Hook Usage**

```typescript
// OLD - Next.js patterns
const router = useRouter()
const locale = useLocale()
const t = useTranslations('namespace')
const params = useParams<{ id: string }>()

// Navigate
router.push('/some-path')

// NEW - React Router patterns
const navigate = useNavigate()
const { t, i18n } = useTranslation(['namespace'])
const { id } = useParams<{ id: string }>()
const dir = getLangDir(i18n.language) // For RTL support

// Navigate  
navigate('/some-path')
```

### **Step 3: Replace Server Actions with API Hooks**

#### **Option A: Custom Hook Pattern (Recommended)**

```typescript
// Create custom hook: hooks/useSomeData.ts
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext'

export const useSomeData = (id: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['some-data', id],
        queryFn: async () => {
            if (!id) return null
            
            const response = await apiClient.get(`/api/some-endpoint/${id}`)
            return response.data
        },
        enabled: !!id && !!apiClient
    })
}

// Use in component:
const { data, isLoading, error } = useSomeData(courseId)
```

#### **Option B: Direct TanStack Query Usage**

```typescript
// In component:
const apiClient = useAuthApiClient()

const { data, isLoading, error } = useQuery({
    queryKey: ['some-data', id],
    queryFn: async () => {
        const response = await apiClient.get(`/api/some-endpoint/${id}`)
        return response.data
    },
    enabled: !!id && !!apiClient
})
```

### **Step 4: Update Store Integration**

**For stores that need API data:**

```typescript
// OLD - Store with server action calls
export const useMyStore = create<StoreType>(set => ({
    fetchData: async (id: string) => {
        const data = await serverAction(id)
        set({ data })
    }
}))

// NEW - Simplified store + custom hook pattern
export const useMyStore = create<StoreType>(set => ({
    setData: (data: DataType[]) => set({ data }),
    // Remove fetchData - handle in component with hook
}))

// In component:
const { data } = useCustomHook(id)
const setData = useMyStore(state => state.setData)

useEffect(() => {
    if (data) {
        setData(data)
    }
}, [data, setData])
```

### **Step 5: Handle RTL Support**

Always maintain RTL (right-to-left) language support:

```typescript
const { t, i18n } = useTranslation(['namespace'])
const dir = getLangDir(i18n.language)

// Apply to containers:
<div dir={dir}>
  {/* content */}
</div>

// Apply to conditional styling:
<div className={cn(dir === 'rtl' && 'flex-row-reverse')}>
  {/* content */}
</div>
```

## 🛠 Common Migration Patterns

### **Pattern 1: Page Component Migration**

```typescript
// OLD - Next.js page
'use client'
import { useParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useEffect, useState } from 'react'
import { getSomeData } from '@/lib/actions'

export default function SomePage() {
    const params = useParams<{ id: string }>()
    const t = useTranslations('namespace')
    const [data, setData] = useState(null)
    
    useEffect(() => {
        getSomeData(params.id).then(setData)
    }, [params.id])
    
    return <div>{/* content */}</div>
}

// NEW - React Router component
import React from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { useSomeData } from '../hooks/useSomeData'

export default function SomePage() {
    const { id } = useParams<{ id: string }>()
    const { t, i18n } = useTranslation(['namespace'])
    const dir = getLangDir(i18n.language)
    
    const { data, isLoading, error } = useSomeData(id)
    
    if (isLoading) return <div>Loading...</div>
    if (error) return <div>Error loading data</div>
    
    return <div dir={dir}>{/* content */}</div>
}
```

### **Pattern 2: Form Handling with Mutations**

```typescript
// NEW - Form with TanStack Query mutations
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext'

const CreateItemForm = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()
    
    const createMutation = useMutation({
        mutationFn: async (data: FormData) => {
            const response = await apiClient.post('/api/items', data)
            return response.data
        },
        onSuccess: () => {
            // Invalidate and refetch related queries
            queryClient.invalidateQueries({ queryKey: ['items'] })
        }
    })
    
    const handleSubmit = (formData: FormData) => {
        createMutation.mutate(formData)
    }
    
    return (
        <form onSubmit={handleSubmit}>
            {/* form fields */}
            <button 
                type="submit" 
                disabled={createMutation.isPending}
            >
                {createMutation.isPending ? 'Creating...' : 'Create'}
            </button>
        </form>
    )
}
```

### **Pattern 3: URL State Management**

```typescript
// Handling URL search parameters
import { useSearchParams } from 'react-router-dom'

const ComponentWithTabs = () => {
    const [searchParams, setSearchParams] = useSearchParams()
    const activeTab = searchParams.get('tab') || 'default'
    
    const handleTabChange = (tab: string) => {
        const newParams = new URLSearchParams(searchParams)
        newParams.set('tab', tab)
        setSearchParams(newParams)
    }
    
    return (
        <Tabs value={activeTab} onValueChange={handleTabChange}>
            {/* tabs content */}
        </Tabs>
    )
}
```

## 📁 File Structure Convention

### **Recommended folder structure for migrated pages:**

```
apps/dinobot/src/app/pages/[PageName]/
├── [PageName].tsx              # Main page component
├── hooks/                      # Custom hooks for data fetching
│   ├── use[PageName]Data.ts   # Main data hook
│   └── use[SpecificFeature].ts # Feature-specific hooks
├── stores/                     # Zustand stores (if needed)
│   └── [pageName].store.ts    
├── components/                 # Page-specific components
│   ├── [Feature]/
│   │   ├── [feature].tsx      # Main feature component
│   │   └── [feature]-*.tsx    # Sub-components
└── types/                      # TypeScript types (if needed)
    └── [pageName].types.ts
```

## 🔧 Available Backend APIs

### **Current Available Backend APIs:**

**⚠️ IMPORTANT: Always check the backend before starting migration!**

**How to verify available routes:**
1. Check `apps/dinobot-backend/src/app.ts` for registered routes
2. Browse `apps/dinobot-backend/src/lib/` for implemented modules
3. Review backend `CLAUDE.md` for migration status and progress
4. Compare with `dinobot-old/src/app/api/` for missing endpoints

**Currently Available Endpoints:**
```
GET/POST /api/auth/*              - Authentication
GET/POST /api/domains/*           - Domain management
GET/POST /api/levels/*            - Level management  
GET/POST /api/domain-level/*      - Domain-Level relationships
GET/POST /api/chapters/*          - Chapter management
GET/POST /api/exams/*             - Exam and exercise management
GET/POST /api/training-mode/*     - Training mode functionality
GET/POST /api/training/*          - Training exercises
POST     /api/control-generator/* - AI-powered question generation
CRUD     /api/evaluation-scheduler/* - Evaluation and notation management (COMPLETE)
CRUD     /api/control-mode/*       - Control/evaluation management
GET      /api/events/*            - Calendar events
POST     /api/chat               - AI chat functionality
CRUD     /api/classes/*           - Class management
CRUD     /api/themes/*            - Theme management
```

**If the required API doesn't exist:**
- Check if it needs to be migrated from `dinobot-old/src/app/api/`
- Follow the backend migration guide in `apps/dinobot-backend/CLAUDE.md`
- Create new Hono.js routes using the established patterns

### **API Client Usage:**
```typescript
// Always use the authenticated API client
const apiClient = useAuthApiClient()

// GET request
const response = await apiClient.get('/api/endpoint')

// POST request  
const response = await apiClient.post('/api/endpoint', data)

// With query parameters
const response = await apiClient.get('/api/endpoint', {
    params: { page: 1, limit: 10 }
})
```

## ✅ Migration Checklist

For each migrated page/component:

**Pre-Migration:**
- [ ] 🔍 **Check old DinoBot**: Reference `dinobot-old/src/app/` for original implementation
- [ ] 🔍 **Check backend routes** in `apps/dinobot-backend/src/app.ts`
- [ ] 📋 **Verify API endpoints** exist in `apps/dinobot-backend/src/lib/`
- [ ] 📖 **Review backend CLAUDE.md** for migration status
- [ ] 🔧 **Create missing APIs** if needed (following Hono.js patterns)

**Component Migration:**
- [ ] ✅ Remove `'use client'` directives
- [ ] ✅ Replace `import { useTranslations } from 'next-intl'` → `import { useTranslation } from 'react-i18next'`
- [ ] ✅ Replace `import { useLocale } from 'next-intl'` → `useTranslation().i18n.language`
- [ ] ✅ Replace `import { useParams } from 'next/navigation'` → `import { useParams } from 'react-router-dom'`
- [ ] ✅ Replace `import { useRouter } from '@/i18n/routing'` → `import { useNavigate } from 'react-router-dom'`
- [ ] ✅ Replace `import { useSearchParams } from 'next/navigation'` → `import { useSearchParams } from 'react-router-dom'`
- [ ] ✅ Remove `import Image from 'next/image'` (use regular img tags)
- [ ] ✅ Replace server action imports with API client + TanStack Query
- [ ] ✅ Add RTL support with `getLangDir(i18n.language)`
- [ ] ✅ Use `useAuthApiClient()` from `@dinobot/frontend/app/contexts/AppContext`
- [ ] ✅ Handle loading and error states properly
- [ ] ✅ Update import paths to use monorepo structure
- [ ] ✅ Update hook calls: `t = useTranslations()` → `{ t } = useTranslation()`

**Testing:**
- [ ] ✅ Test functionality in both LTR and RTL languages
- [ ] ✅ Verify URL state management works correctly
- [ ] ✅ Check that navigation between pages works
- [ ] 🧪 **Test API endpoints** work with the new frontend
- [ ] ✅ Run `nx lint dinobot --fix` and `nx typecheck dinobot`

## 🚨 Common Pitfalls to Avoid

1. **Don't modify translation keys** - Keep exact same keys as original from `dinobot-old/`
2. **Don't remove RTL support** - Always use `getLangDir()` and `dir` attributes
3. **Don't hardcode API URLs** - Use proper backend endpoints
4. **Don't skip error handling** - Add loading/error states for queries
5. **Don't pass API client to stores** - Use custom hooks instead
6. **Don't forget query invalidation** - Update related queries after mutations
7. **Don't mix old and new patterns** - Fully migrate each component
8. **Don't forget to check dinobot-old/** - Always reference the original implementation
9. **Don't skip backend verification** - Ensure APIs exist before migrating frontend

## 🔄 Store Migration Pattern

### **Before (with API calls in store):**
```typescript
export const useMyStore = create<StoreType>(set => ({
    data: [],
    fetchData: async (id: string, apiClient: any) => {
        const response = await apiClient.get(`/api/data/${id}`)
        set({ data: response.data })
    }
}))
```

### **After (store + custom hook):**
```typescript
// Store (simplified, no API calls)
export const useMyStore = create<StoreType>(set => ({
    data: [],
    setData: (data: DataType[]) => set({ data })
}))

// Custom Hook (handles API calls)
export const useMyData = (id: string | undefined) => {
    const apiClient = useAuthApiClient()
    
    return useQuery({
        queryKey: ['my-data', id],
        queryFn: async () => {
            if (!id) return []
            const response = await apiClient.get(`/api/data/${id}`)
            return response.data
        },
        enabled: !!id && !!apiClient
    })
}

// Component (connects hook to store)
const MyComponent = () => {
    const setData = useMyStore(state => state.setData)
    const { data } = useMyData(id)
    
    useEffect(() => {
        if (data) setData(data)
    }, [data, setData])
}
```

## 🚧 CURRENT MIGRATION STATUS

### **✅ COMPLETED MIGRATIONS:**

#### **MyCourses (Student Section) - COMPLETE**
- `apps/dinobot/src/app/pages/MyCourses/MyCourses.tsx`
- `apps/dinobot/src/app/pages/MyCourses/Course/components/about/about-course.tsx`
- `apps/dinobot/src/app/pages/MyCourses/Course/components/notation/scoring.tsx`
- `apps/dinobot/src/app/pages/MyCourses/hooks/useMyCourses.ts`
- `apps/dinobot/src/app/pages/MyCourses/hooks/useNotation.ts`

#### **TeacherEvaluation - IN PROGRESS** 
**Status**: ✅ All Next.js patterns migrated (imports, hooks, components), ⚠️ Server actions → React Query hooks migration PARTIALLY COMPLETE

**Main Entrypoints (✅ Functional):**
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/create-evaluation/CreateEvaluation.tsx`
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/create-evaluation-next/CreateEvaluationNext.tsx`
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/schedule-assessment/ScheduleAssessment.tsx`

**Routes:**
- `/my-classes/:classId/create-evaluation` → CreateEvaluation
- `/my-classes/:classId/create-evaluation-next` → CreateEvaluationNext
- `/my-classes/:classId/schedule-assessment` → ScheduleAssessment

**Backend APIs (✅ Available):**
- `/api/evaluation-scheduler/*` - Complete CRUD operations
- `/api/control-generator/*` - AI-powered question generation
- `/api/control-mode/*` - Exercise and evaluation management
- `/api/themes/*` - Theme management

**✅ COMPLETED Pattern Migrations:**
- ✅ All `'use client'` directives removed
- ✅ All `next-intl` → `react-i18next` conversions (45+ files updated)
- ✅ All `next/navigation` → `react-router-dom` conversions
- ✅ All `@/components/ui/*` → `@dinobot/components-ui` imports updated
- ✅ All `@/lib/utils/utils` → `@dinobot/utils` imports updated
- ✅ Translation patterns: `useTranslations('path.with.dots')` → `useTranslation('path/with/slashes')`
- ✅ Locale handling: `useLocale()` → `useTranslation().i18n.language`
- ✅ RTL support maintained with `getLangDir(i18n.language)`

**✅ COMPLETED Server Action Migrations:**
- ✅ `schedule-assessment/components/select-evaluation.tsx` → Uses `useControls` hook
- ✅ `create-evaluation-next/@title/page.tsx` → Uses `useControlWithMedia` and `useClass` hooks  
- ✅ `create-evaluation-next/components/dialogs/components/chapter-list-part.tsx` → Uses `useChaptersByDomainAndLevel` and `usePartsByChapter` hooks
- ✅ `create-evaluation-next/components/dialogs/components/question-content.tsx` → Uses `useGenerateQuestions` hook
- ✅ `create-evaluation-next/components/question-content-result.tsx` → Uses `useGenerateControlQuestions` hook
- ✅ `create-evaluation-next/components/dialogs/components/question-content-db.tsx` → Uses `useSearchSimilarQuestions` hook
- ✅ `create-evaluation/components/param-evaluation.tsx` → Uses `useClassById`, `useThemesByClassId`, `useCreateTheme` hooks
- ✅ `schedule-assessment/components/evaluation-form.tsx` → Uses `useClassById`, `useAddPlannedEvaluation` hooks
- ✅ `create-evaluation-next/components/dialogs/dialog-exo-questions-from-exams.tsx` → Uses React Query with centralized types
- ✅ `create-evaluation-next/components/dialogs/components/questions-from-exam-Table.tsx` → Uses centralized types (`ExoOutput`, `ExoSortsAndFilters`, `SortColumn`)

**✅ CUSTOM HOOKS CREATED:**
- `useControls`, `useControlWithMedia`, `useClass`, `useChaptersByDomainAndLevel`, `usePartsByChapter`, `useGenerateControlQuestions`, `useSearchSimilarQuestions`, `useClassById`, `useThemesByClassId`, `useCreateTheme`, `useAddPlannedEvaluation`

**⚠️ REMAINING WORK:**
- **Import Path Updates**: Several files still have old `@/lib` and `@/prisma` import paths that need updating to monorepo structure  
- **Server Action Migration**: Some components still use server actions:
  - `dialog-exo-questions-from-file.tsx` - needs server action conversion
  - `dialog-params-evaluation.tsx` - uses `getClassById`, `getDomainsByLevel`
- **Form Submission Patterns**: Update remaining form handlers to use `useMutation` instead of server actions


### **📋 TODO QUEUE:**
- **HIGH PRIORITY**: Convert remaining server actions in `dialog-exo-questions-from-file.tsx` and `dialog-params-evaluation.tsx`
- **HIGH PRIORITY**: Create `useGetDomainsByLevel` hook for remaining server action usage  
- **MEDIUM**: Update all remaining `@/lib` and `@/prisma` import paths to monorepo structure
- **MEDIUM**: Test TeacherEvaluation complete workflows end-to-end
- **LOW**: Migrate other Teacher pages (if any exist)
- **LOW**: Migrate Student pages (if any exist)

### **🎯 IMMEDIATE NEXT STEPS FOR AI AGENTS:**
1. **✅ COMPLETED**: Created centralized `teacherEvaluation.types.ts` file with all types consolidated
2. **✅ COMPLETED**: Updated all hooks to use relative import for `useAuthApiClient`
3. **✅ COMPLETED**: Extracted missing types from backend and consolidated imports
4. **✅ COMPLETED**: Converted major server actions (`addPlannedEvaluation`, `getClassById`) to React Query hooks
5. **🔄 IN PROGRESS**: Convert remaining server actions in dialog components
6. **📋 TODO**: Update import paths - Replace old `@/lib` and `@/prisma` imports with proper monorepo paths
7. **📋 TODO**: Test functionality - Ensure all components work after migrations

## 📚 Reference Examples

### **Perfectly Migrated Components (Use as Templates):**
- `apps/dinobot/src/app/pages/MyCourses/MyCourses.tsx` - Complete page migration
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/hooks/useEvaluationData.ts` - Custom TanStack Query hook
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/store/evaluation-params.store.ts` - Modern Zustand store
- `apps/dinobot/src/app/pages/Teacher/TeacherEvaluation/components/layout-evaluation.tsx` - Layout with proper React Router patterns

These files demonstrate the complete migration pattern and should be referenced for future migrations.

---

## 🎯 Quick Migration Workflow

1. **Find original implementation** in `dinobot-old/src/app/` directory
2. **Check backend APIs** - ensure required endpoints exist:
   - Check `apps/dinobot-backend/src/app.ts` for registered routes
   - Look in `apps/dinobot-backend/src/lib/` for existing modules
   - Refer to backend CLAUDE.md for migration status
   - Create new routes if needed following the established Hono.js patterns
3. **Create the new component** in `apps/dinobot/src/app/pages/` following React Router patterns
4. **Apply Next.js to React Router replacements** (see checklist above)
5. **Create custom hooks** for data fetching instead of server actions
6. **Update stores** to remove API logic (if any)
7. **Test thoroughly** with different languages and user roles
8. **Run linting and type checking**: `nx lint dinobot --fix` and `nx typecheck dinobot`
9. **Update this migration guide** with progress and new examples

## 📝 For New Claude Code Sessions

When starting a new Claude Code conversation for DinoBot migration work:

1. **Reference this guide** for current migration status and patterns
2. **Check the "CURRENT MIGRATION STATUS"** section above for what's completed/in-progress
3. **Always look at `dinobot-old/`** directory for original implementations
4. **Use completed migrations as templates** (listed in Reference Examples)
5. **Update this guide** when completing migrations or finding new patterns

This guide should be sufficient for migrating most pages from the old Next.js app to the new React Router architecture!