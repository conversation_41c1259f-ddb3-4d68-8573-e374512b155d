import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';


const InvalidResetKey: React.FC = () => {
  const { t } = useTranslation(['app/auth']);

  return (
    <div className="w-full flex flex-col items-center">
      <h2 className="text-dinoBotBlue text-5xl font-bold my-10">
        {t('reset-password.invalid.title')}
      </h2>
      <p className="mb-10">{t('reset-password.invalid.text')}</p>

      <button className="bg-transparent border border-gray-500 rounded-2xl text-gray-500 hover:bg-gray-500 hover:text-white ml-2 transition-all duration-300 px-4 py-2">
        <Link to="/">{t('reset-password.invalid.return')}</Link>
      </button>
    </div>
  );
};

export default InvalidResetKey;
