import { Button } from '@dinobot/components-ui'
import { Input } from '@dinobot/components-ui'
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    SortingState,
    useReactTable
} from '@tanstack/react-table'
import { ChevronUp, ChevronDown, Search } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import React, { useEffect, useState } from 'react'
import { ExoOutput, ExoSortsAndFilters, SortColumn } from '../../../../teacherEvaluation.types'

type QuestionsFromExamTableProps = {
    exams: ExoOutput[]
    setExoId: React.Dispatch<React.SetStateAction<string | null>>
    filtersSortChange: (filters: ExoSortsAndFilters) => void
}

const QuestionsFromExamTable = ({
    exams,
    filtersSortChange,
    setExoId
}: QuestionsFromExamTableProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/exam')
    const [sorting, setSorting] = useState<SortingState>([])
    const [sortAndFilters, setSortAndFilters] = useState<ExoSortsAndFilters>({
        sort: { column: 'none', order: 'asc' },
        filters: {},
        pagination: { skip: 0 }
    })
    const [search, setSearch] = useState('')
    const [columnVisibility] = useState({ id: false })
    const [selectedExam, setSelectedExam] = useState<string | null>(null)

    const columns: ColumnDef<ExoOutput>[] = [
        {
            accessorKey: 'id'
        },
        {
            accessorFn: row => row.title,
            id: 'exam',
            header: t('name'),
            cell: ({ row }) => {
                const content = row.getValue('exam')
                return (
                    <div>
                        {content ? (
                            `${content}`
                        ) : (
                            <span className="italic text-dinoBotRed">
                                Non renseigné
                            </span>
                        )}
                    </div>
                )
            }
        },
        // {
        //     // accessorFn: (row) => row.,
        //     id:"center",
        //     header:"Centre d’examen",
        //     cell:({row})=>{
        //         const content=row.getValue('center')
        //         return (
        //             <div>
        //                 {content ? (
        //                 `${content}`
        //                 ) : (
        //                 <span className="italic text-dinoBotRed">Non renseigné</span>
        //                 )}
        //             </div>
        //         )
        //     }
        // },
        {
            accessorFn: row => row.exam.year,
            id: 'year',
            header: t('year'),
            cell: ({ row }) => {
                const content = row.getValue('year')
                return (
                    <div>
                        {content ? (
                            `${content}`
                        ) : (
                            <span className="italic text-dinoBotRed">
                                Non renseigné
                            </span>
                        )}
                    </div>
                )
            }
        }
    ]
    const table = useReactTable({
        data: exams,
        columns,
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
        onSortingChange: setSorting,
        state: {
            columnVisibility,
            sorting
        }
    })

    useEffect(() => {
        setSortAndFilters(state => ({
            ...state,
            sort: sorting[0]
                ? {
                      column: sorting[0].id as SortColumn,
                      order: sorting[0].desc ? 'desc' : 'asc'
                  }
                : {
                      column: 'none',
                      order: 'asc'
                  },
            pagination: { skip: undefined, take: undefined }
        }))
    }, [sorting])

    useEffect(() => {
        if (selectedExam) {
            // here it sets the exoId instead of the examId, it might be confusing but its the right way since the table is a list of exercises of all exams
            setExoId(table.getRow(selectedExam).getValue('id'))
        }
    }, [selectedExam])

    useEffect(() => {
        filtersSortChange(sortAndFilters)
    }, [sortAndFilters])
    const onSearch = () => {
        setSortAndFilters(filter => ({
            ...filter,
            filters: { examtitle: search }
        }))
    }
    return (
        <div className="h-full">
            <div className="flex p-1 pb-4">
                <Input
                    className=""
                    placeholder="Chercher"
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                />
                <Button variant={'link'} onClick={() => onSearch()}>
                    <Search />
                </Button>
            </div>
            <div
                className={`min-w-96 w-full h-5  ${table?.getRowModel()?.rows?.length <= 3 ? 'pr-5' : 'pr-[26px]'} flex  text-[10px]`}
            >
                {table.getHeaderGroups().map(headerGroup => (
                    <div
                        key={headerGroup.id}
                        className="flex-1 flex items-center bg-dinoBotLightGray"
                    >
                        {headerGroup.headers.map((header, index) => {
                            return (
                                <div
                                    key={header.id}
                                    className={`border border-dinoBotGray/60 h-5 ${index === 0 ? 'flex-1' : 'w-1/5'} ${
                                        header.column.getCanSort()
                                            ? 'cursor-pointer select-none'
                                            : ''
                                    }`}
                                    title={
                                        header.column.getCanSort()
                                            ? header.column.getNextSortingOrder() ===
                                              'asc'
                                                ? 'Sort ascending'
                                                : header.column.getNextSortingOrder() ===
                                                    'desc'
                                                  ? 'Sort descending'
                                                  : 'Clear sort'
                                            : undefined
                                    }
                                >
                                    <div className="size-full px-1 flex flex-col justify-start items-center text-dinoBotDarkGray font-semibold">
                                        <div
                                            className="size-full  flex  justify-between items-center gap-1"
                                            onClick={header.column.getToggleSortingHandler()}
                                        >
                                            <div>
                                                {header.isPlaceholder
                                                    ? null
                                                    : flexRender(
                                                          header.column
                                                              .columnDef.header,
                                                          header.getContext()
                                                      )}
                                            </div>
                                            <div className="h-full flex flex-col ">
                                                <ChevronUp className="size-3 " />
                                                <ChevronDown className="size-3" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                ))}
            </div>
            <div className="relative h-4/5 pr-5 overflow-x-hidden overflow-y-auto custom-scroller">
                <div className="w-full border border-dinoBotGray/60 border-y-0 ">
                    {table.getRowModel().rows?.length ? (
                        table.getRowModel().rows.map((row, rIndex) => (
                            <div
                                key={row.id}
                                data-state={row.getIsSelected() && 'selected'}
                                className={`flex flex-col  items-center cursor-pointer  ${selectedExam === row.id ? 'bg-dinoBotVibrantBlue/20 text-dinoBotBlue hover:bg-dinoBotVibrantBlue/30' : 'text-dinoBotGray hover:bg-dinoBotGray/10'}`}
                                onClick={() => {
                                    setSelectedExam(row.id)
                                }}
                            >
                                <div className="w-full flex text-xs  py-3">
                                    {row
                                        .getVisibleCells()
                                        .map((cell, index) => (
                                            <div
                                                key={cell.id}
                                                className={` ${index === 0 ? 'pl-4 flex-1' : 'pl-2 w-1/5'}`}
                                            >
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </div>
                                        ))}
                                </div>
                                {rIndex <
                                    table.getRowModel().rows.length - 1 && (
                                    <div className="w-[calc(100%-32px)] h-[1px] bg-dinoBotLightGray"></div>
                                )}
                            </div>
                        ))
                    ) : (
                        <div>
                            <div className="h-24 text-center">
                                {t('no_exam')}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div
                className={` ${table.getRowModel().rows?.length <= 3 ? 'w-[calc(100%-20px)]' : 'w-[calc(100%-26px)]'}  h-[1px] bg-dinoBotGray/60`}
            ></div>
        </div>
    )
}

export default QuestionsFromExamTable
