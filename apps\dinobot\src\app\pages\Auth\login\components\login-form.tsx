import React, { useState } from 'react'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { Input, PasswordVisibility } from '@dinobot/components-ui'
import { useLogin } from '../hooks/useLogin'
import { LoginFormData } from '../login.types'
import { Link, useNavigate } from 'react-router-dom'


interface LoginFormProps {
    registrationType?: 'none' | 'open' | 'invitation' | 'admin'
}

export default function LoginForm({ registrationType = 'none' }: LoginFormProps) {
    const navigate = useNavigate();
    const [formData, setFormData] = useState<LoginFormData>({
        email: '',
        password: '',
        remember: false
    })
    const [showPassword, setShowPassword] = useState(false)

    // Translation hook - using 'app' namespace for the app folder
    const { t } = useTranslation(['app/auth'])

    const loginMutation = useLogin()
    const isValidRegistrationType = registrationType !== 'none'

    // Exact same function from original
    const handleShowPassword =
        (setShowPassword: React.Dispatch<React.SetStateAction<boolean>>) =>
        (password: boolean) => {
            setShowPassword(!password)
        }

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value, type, checked } = e.target
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }))
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        try {
            await loginMutation.mutateAsync(formData)
            toast.success(t('login.success'))
            navigate('/')
        } catch (error: any) { /* empty */ }
    }

    return (
        <div
            className="relative bg-white size-full flex flex-col gap-11 justify-evenly items-center overflow-auto custom-scroller pb-10"
            style={{ backgroundImage: "url('/bg-with-icons.webp')" }}
        >
            <img
                src={'/auth-bg-icons.webp'}
                alt="oui active"
                width={908}
                height={465}
                className="absolute hidden md:block top-0 left-1/2 -translate-x-1/2"
            />
            <div className="mt-14">
                <img
                    src={'/oui-active.svg'}
                    alt="oui active"
                    width={322}
                    height={55}
                    className="z-50"
                />
            </div>
            <div className="relative sm:w-3/4 md:w-[450px]">
                <form
                    onSubmit={handleSubmit}
                    className="flex flex-col items-center gap-4 space-y-3 w-full"
                >
                    <div className="w-full flex-1 rounded-lg border drop-shadow-md px-6 pb-4 pt-8 shadow-sm dark:bg-zinc-950 z-50 bg-clip-padding backdrop-blur-md backdrop-opacity-90 bg-[#f5f5f5] saturate-100 backdrop-contrast-100">
                        <h1 className="mb-3 text-3xl font-bold text-center text-slate-800">
                            {t('login.title')}
                        </h1>
                        <div className="w-full mt-10">
                            <div>
                                <label
                                    className="block text-lg font-medium text-dinoBotBlue"
                                    htmlFor="email"
                                >
                                    {t('login.email')}
                                    <span className="text-dinoBotRed font-semibold">
                                        *
                                    </span>
                                </label>
                                <div className="relative">
                                    <input
                                        className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                        id="email"
                                        type="email"
                                        name="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        placeholder={t('login.email-placeholder')}
                                        required
                                    />
                                </div>
                            </div>
                            <div className="mt-4">
                                <label
                                    className="block text-lg font-medium text-dinoBotBlue"
                                    htmlFor="password"
                                >
                                    {t('login.password')}
                                    <span className="text-dinoBotRed font-semibold">
                                        *
                                    </span>
                                </label>
                                <div className="relative">
                                    <Input
                                        className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        name="password"
                                        value={formData.password}
                                        onChange={handleChange}
                                        placeholder={t('login.psw-placeholder')}
                                        required
                                        minLength={6}
                                        inputAdornment={
                                            <PasswordVisibility
                                                passwordVisibility={showPassword}
                                                showPassword={() => {
                                                    handleShowPassword(setShowPassword)(showPassword)
                                                }}
                                                hidePassword={() => {
                                                    handleShowPassword(setShowPassword)(showPassword)
                                                }}
                                            />
                                        }
                                    />
                                    <div className="w-full flex flex-row justify-between mt-1">
                                        <div className="flex flex-row gap-1 text-xs text-dinoBotVibrantBlue hover:text-dinoBotBlue hover:underline transition-all duration-300">
                                            <input
                                                type="checkbox"
                                                name="remember"
                                                id="remember"
                                                checked={formData.remember}
                                                onChange={handleChange}
                                            />
                                            <label
                                                htmlFor="remember"
                                                className="font-semibold"
                                            >
                                                {t('login.remember')}
                                            </label>
                                        </div>
                                        <Link
                                            to="/reset-password"
                                            className="flex flex-row gap-1 text-xs text-dinoBotVibrantBlue hover:text-dinoBotBlue hover:underline transition-all duration-300"
                                        >
                                            <div className="font-semibold">
                                                {t('login.forget')}
                                            </div>
                                        </Link>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="w-full flex justify-center">
                            <button
                                type="submit"
                                disabled={loginMutation.isPending}
                                className="flex flex-row justify-center items-center my-4 h-10 w-32 bg-dinoBotVibrantBlue text-white rounded-xl border hover:text-white transition-all duration-300"
                                aria-disabled={loginMutation.isPending}
                            >
                                {loginMutation.isPending ? (
                                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                                ) : (
                                    t('login.submit')
                                )}
                            </button>
                        </div>
                        {isValidRegistrationType && (
                            <Link
                                to="/signup"
                                className="flex flex-row gap-1 justify-center items-center text-xs text-dinoBotGray w-full mt-4"
                            >
                                <div>{t('login.havent')}</div>
                                <div className="text-dinoBotVibrantBlue underline">
                                    {t('login.inscription')}
                                </div>
                            </Link>
                        )}
                    </div>
                </form>
                <img
                    src={'/dinobot-all.svg'}
                    alt="dinobot"
                    width={260}
                    height={280}
                    className="absolute bottom-4 left-3/4"
                />
                <img
                    src={'/dinobot-tv.svg'}
                    alt="dinobot"
                    width={175}
                    height={175}
                    className="absolute bottom-10 right-[90%]"
                />
            </div>
        </div>
    )
}
