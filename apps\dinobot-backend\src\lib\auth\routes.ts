import { Hono } from 'hono'
import { set<PERSON><PERSON>ie, deleteC<PERSON>ie, getCookie } from 'hono/cookie'
import { z } from 'zod'
import { logger } from '@/lib/logger'
import { requireAuth, optionalAuth, getAuthUser, getAuthContext } from './middleware'
import { authenticateUser, refreshAccessToken } from './auth'
import { LoginData } from './types'
import { generateToken, generateSalt, hashPassword, verifyPassword, createTokenExpiry, isTokenExpired } from './utils'
import { sendVerificationEmail, sendPasswordResetEmail } from '@/lib/email/service'
import prisma from '@/common/database/prisma'
import { UserTypeSchema } from '@dinobot/prisma'

type UserType = (typeof UserTypeSchema)['_type']

const authRoutes = new Hono()

// Login endpoint - generate JWT tokens
authRoutes.post('/login', async (c) => {
    try {
        const body = await c.req.json()

        const loginSchema = z.object({
            email: z.string().email(),
            password: z.string().min(6),
            remember: z.boolean().optional().default(false)
        })

        const result = loginSchema.safeParse(body)
        if (!result.success) {
            return c.json({
                error: 'Invalid input format',
                details: result.error.issues
            }, 400)
        }

        const loginData: LoginData = result.data

        // Authenticate user and generate tokens
        const tokenPair = await authenticateUser(loginData)
        if (!tokenPair) {
            return c.json({
                error: 'Invalid email or password'
            }, 401)
        }

        // Set secure HTTP-only cookie for access token
        setCookie(c, 'token', tokenPair.accessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: tokenPair.expiresIn
        })

        // Set refresh token cookie
        setCookie(c, 'refreshToken', tokenPair.refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 7 * 24 * 60 * 60 // 7 days
        })

        setCookie(c, 'user', JSON.stringify(tokenPair.user), {
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60, // 7 days
        });

        return c.json({
            success: true,
            message: 'Login successful',
            expiresIn: tokenPair.expiresIn
        })

    } catch (error) {
        logger.error({ error }, 'Login error')
        return c.json({
            error: 'Login failed'
        }, 500)
    }
})

// Logout endpoint - clear cookies
authRoutes.post('/logout', async (c) => {
    try {
        // Clear auth cookies
        deleteCookie(c, 'token')
        deleteCookie(c, 'refreshToken')
        deleteCookie(c, 'authjs.session-token') // Legacy cookie
        deleteCookie(c, 'user') // Clear user cookie

        return c.json({
            success: true,
            message: 'Logout successful'
        })
    } catch (error) {
        logger.error({ error }, 'Logout error')
        return c.json({
            error: 'Logout failed'
        }, 500)
    }
})

// Get current user info
authRoutes.get('/me', async (c) => {
    try {
        // Check if a token was provided
        const authHeader = c.req.header('Authorization')
        const cookieToken = getCookie(c, 'authjs.session-token') || getCookie(c, 'token')
        const hasToken = (authHeader?.startsWith('Bearer ') || cookieToken)

        if (!hasToken) {
            // Return 403 if no token provided
            return c.json({ error: 'Authentication required' }, 401)
        }

        // Token provided - require valid authentication
        const result = await requireAuth(c, async () => {})
        if (result) {
            // requireAuth returned a response (error)
            return result
        }

        const authContext = getAuthContext(c)
        return c.json({
            user: authContext?.user || null
        })
    } catch (error) {
        logger.error({ error }, 'Get user error')
        return c.json({
            error: 'Invalid or expired token'
        }, 403)
    }
})

// Refresh token endpoint
authRoutes.post('/refresh', async (c) => {
    try {
        const refreshToken = c.req.header('X-Refresh-Token') ||
                           await c.req.text() // Allow refresh token in body

        if (!refreshToken) {
            return c.json({
                error: 'No refresh token provided'
            }, 401)
        }

        const tokenPair = await refreshAccessToken(refreshToken)
        if (!tokenPair) {
            return c.json({
                error: 'Invalid or expired refresh token'
            }, 401)
        }

        // Update cookies with new tokens
        setCookie(c, 'token', tokenPair.accessToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: tokenPair.expiresIn
        })

        setCookie(c, 'refreshToken', tokenPair.refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 7 * 24 * 60 * 60
        })

        return c.json({
            success: true,
            expiresIn: tokenPair.expiresIn
        })

    } catch (error) {
        logger.error({ error }, 'Token refresh error')
        return c.json({
            error: 'Token refresh failed'
        }, 500)
    }
})

// Registration endpoint
authRoutes.post('/register', async (c) => {
    try {
        const body = await c.req.json()

        const registerSchema = z.object({
            email: z.string().email(),
            password: z.string().min(6),
            password2: z.string(),
            type: z.enum(['student', 'teacher', 'establishment']).optional().default('student')
        })

        const result = registerSchema.safeParse(body)
        if (!result.success) {
            return c.json({
                error: 'Invalid input format',
                details: result.error.issues
            }, 400)
        }

        const { email, password, password2, type } = result.data

        if (password !== password2) {
            return c.json({
                error: 'Password confirmation does not match'
            }, 400)
        }

        // Check if user already exists
        const existingUser = await prisma.user.findUnique({
            where: { email }
        })

        if (existingUser) {
            return c.json({
                error: 'User already exists'
            }, 409)
        }

        // Create user with hashed password
        const salt = generateSalt()
        const hashedPassword = await hashPassword(password, salt)
        const verifyKey = generateToken(32)
        const verifyKeyDate = createTokenExpiry(24) // 24 hours

        await prisma.user.create({
            data: {
                email,
                password: hashedPassword,
                salt,
                emailVerified: false,
                verifyKey,
                verifyKeyDate,
                type: type as UserType,
                status: 'ACTIVE'
            }
        })

        // Send verification email
        await sendVerificationEmail({
            email: email,
            firstName: 'User',
            verificationToken: verifyKey,
            locale: 'en'
        })

        return c.json({
            success: true,
            message: 'Account created successfully! Please check your email to verify your account.',
            email
        })

    } catch (error) {
        logger.error({ error }, 'Registration error')
        return c.json({
            error: 'Registration failed'
        }, 500)
    }
})

// Change password (requires authentication)
authRoutes.post('/change-password', requireAuth, async (c) => {
    try {
        const user = getAuthUser(c)
        if (!user) {
            return c.json({ error: 'User not authenticated' }, 401)
        }

        const body = await c.req.json()

        const changePasswordSchema = z.object({
            currentPassword: z.string().min(6),
            newPassword: z.string().min(6),
            newPassword2: z.string()
        })

        const result = changePasswordSchema.safeParse(body)
        if (!result.success) {
            return c.json({
                error: 'Invalid input format',
                details: result.error.issues
            }, 400)
        }

        const { currentPassword, newPassword, newPassword2 } = result.data

        if (newPassword !== newPassword2) {
            return c.json({
                error: 'New password confirmation does not match'
            }, 400)
        }

        // Get current password data
        const dbUser = await prisma.user.findUnique({
            where: { id: user.id },
            select: { password: true, salt: true }
        })

        if (!dbUser) {
            return c.json({ error: 'User not found' }, 404)
        }

        // Verify current password
        const isValidPassword = await verifyPassword(currentPassword, dbUser.password, dbUser.salt)
        if (!isValidPassword) {
            return c.json({
                error: 'Current password is incorrect'
            }, 400)
        }

        // Hash new password
        const newSalt = generateSalt()
        const hashedNewPassword = await hashPassword(newPassword, newSalt)

        // Update password
        await prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedNewPassword,
                salt: newSalt
            }
        })

        return c.json({
            success: true,
            message: 'Password changed successfully'
        })

    } catch (error) {
        logger.error({ error }, 'Change password error')
        return c.json({
            error: 'Failed to change password'
        }, 500)
    }
})

// Email verification endpoint - validates token and redirects to profile completion
authRoutes.get('/verify-email/:token', async (c) => {
    try {
        const token = c.req.param('token')

        const user = await prisma.user.findFirst({
            where: { verifyKey: token },
            select: {
                id: true,
                email: true,
                emailVerified: true,
                verifyKeyDate: true
            }
        })

        if (!user || !user.verifyKeyDate) {
            return c.json({
                error: 'Invalid verification token'
            }, 400)
        }

        // Check if token is expired
        if (isTokenExpired(user.verifyKeyDate)) {
            return c.json({
                error: 'Verification token has expired'
            }, 400)
        }

        // Check if already verified
        if (user.emailVerified) {
            return c.json({
                success: true,
                message: 'Email already verified',
                redirectTo: '/login'
            })
        }

        // Redirect to profile completion
        return c.json({
            success: true,
            message: 'Email verification successful! Please complete your profile.',
            redirectTo: `/complete-profile/${token}`
        })

    } catch (error) {
        logger.error({ error }, 'Email verification error')
        return c.json({
            error: 'Email verification failed'
        }, 500)
    }
})

// Resend verification email
authRoutes.post('/resend-verification', async (c) => {
    try {
        const { email } = await c.req.json()

        const user = await prisma.user.findUnique({
            where: { email }
        })

        if (!user) {
            return c.json({
                error: 'User not found'
            }, 404)
        }

        if (user.emailVerified) {
            return c.json({
                error: 'Email already verified'
            }, 400)
        }

        const verifyKey = generateToken(32)
        const verifyKeyDate = createTokenExpiry(24)

        await prisma.user.update({
            where: { id: user.id },
            data: {
                verifyKey,
                verifyKeyDate
            }
        })

        await sendVerificationEmail({
            email: email,
            firstName: 'User',
            verificationToken: verifyKey,
            locale: 'en'
        })

        return c.json({
            success: true,
            message: 'Verification email sent successfully!'
        })

    } catch (error) {
        logger.error({ error }, 'Resend verification error')
        return c.json({
            error: 'Failed to resend verification email'
        }, 500)
    }
})

// Password reset request
authRoutes.post('/reset-password/init', async (c) => {
    try {
        const { email } = await c.req.json()

        const user = await prisma.user.findUnique({
            where: { email }
        })

        if (!user) {
            // Don't reveal if user exists for security
            return c.json({
                success: true,
                message: 'If an account with that email exists, a password reset link has been sent.'
            })
        }

        const resetKey = generateToken(32)
        const resetKeyDate = createTokenExpiry(1) // 1 hour

        await prisma.user.update({
            where: { id: user.id },
            data: {
                resetKey,
                resetKeyDate
            }
        })

        await sendPasswordResetEmail({
            email: email,
            firstName: 'User',
            resetToken: resetKey,
            locale: 'en'
        })

        return c.json({
            success: true,
            message: 'If an account with that email exists, a password reset link has been sent.'
        })

    } catch (error) {
        logger.error({ error }, 'Password reset init error')
        return c.json({
            error: 'Failed to send password reset email'
        }, 500)
    }
})

// Password reset confirmation
authRoutes.post('/reset-password/confirm', async (c) => {
    try {
        const body = await c.req.json()

        const resetSchema = z.object({
            token: z.string(),
            password: z.string().min(6),
            password2: z.string()
        })

        const result = resetSchema.safeParse(body)
        if (!result.success) {
            return c.json({
                error: 'Invalid input format',
                details: result.error.issues
            }, 400)
        }

        const { token, password, password2 } = result.data

        if (password !== password2) {
            return c.json({
                error: 'Password confirmation does not match'
            }, 400)
        }

        const user = await prisma.user.findFirst({
            where: { resetKey: token }
        })

        if (!user || !user.resetKeyDate) {
            return c.json({
                error: 'Invalid reset token'
            }, 400)
        }

        // Check if token is expired
        if (isTokenExpired(user.resetKeyDate)) {
            return c.json({
                error: 'Reset token has expired'
            }, 400)
        }

        // Hash new password
        const salt = generateSalt()
        const hashedPassword = await hashPassword(password, salt)

        await prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedPassword,
                salt,
                resetKey: null,
                resetKeyDate: null
            }
        })

        return c.json({
            success: true,
            message: 'Password reset successfully!'
        })

    } catch (error) {
        logger.error({ error }, 'Password reset confirm error')
        return c.json({
            error: 'Failed to reset password'
        }, 500)
    }
})

// Get profile completion form data (validate verification token)
authRoutes.get('/complete-profile/:token', async (c) => {
    try {
        const token = c.req.param('token')

        const user = await prisma.user.findFirst({
            where: { verifyKey: token },
            select: {
                id: true,
                email: true,
                type: true,
                verifyKey: true,
                emailVerified: true,
                verifyKeyDate: true
            }
        })

        if (!user || !user.verifyKeyDate) {
            return c.json({
                error: 'Invalid verification token'
            }, 400)
        }

        // Check if token is expired
        if (isTokenExpired(user.verifyKeyDate)) {
            return c.json({
                error: 'Verification token has expired'
            }, 400)
        }

        // Check if already verified
        if (user.emailVerified) {
            return c.json({
                error: 'Email already verified and profile completed'
            }, 400)
        }

        // Get available levels for students
        let levels: {id: number, name: string}[] = []
        if (user.type === 'student') {
            levels = await prisma.level.findMany({
                select: {
                    id: true,
                    name: true
                },
                orderBy: { name: 'asc' }
            })
        }

        return c.json({
            success: true,
            user: {
                email: user.email,
                type: user.type
            },
            levels: levels
        })

    } catch (error) {
        logger.error({ error }, 'Get profile completion error')
        return c.json({
            error: 'Failed to load profile completion form'
        }, 500)
    }
})

// Complete user profile after email verification
authRoutes.post('/complete-profile/:token', async (c) => {
    try {
        const token = c.req.param('token')
        const body = await c.req.json()

        // Validate verification token first
        const user = await prisma.user.findFirst({
            where: { verifyKey: token },
            select: {
                id: true,
                email: true,
                type: true,
                verifyKey: true,
                emailVerified: true,
                verifyKeyDate: true
            }
        })

        if (!user || !user.verifyKeyDate) {
            return c.json({
                error: 'Invalid verification token'
            }, 400)
        }

        if (isTokenExpired(user.verifyKeyDate)) {
            return c.json({
                error: 'Verification token has expired'
            }, 400)
        }

        if (user.emailVerified) {
            return c.json({
                error: 'Email already verified and profile completed'
            }, 400)
        }

        // Define validation schema based on user type
        const baseSchema = z.object({
            firstName: z.string().min(1, 'First name is required'),
            lastName: z.string().min(1, 'Last name is required'),
            gender: z.enum(['male', 'female', 'other'])
        })

        let schema = baseSchema
        if (user.type === 'student') {
            schema = baseSchema.extend({
                birthDate: z.string().min(1, 'Birth date is required')
                    .refine((dateStr) => {
                        const date = new Date(dateStr)
                        return date <= new Date()
                    }, 'Birth date cannot be in the future')
                    .refine((dateStr) => {
                        const date = new Date(dateStr)
                        const twelveYearsAgo = new Date()
                        twelveYearsAgo.setFullYear(twelveYearsAgo.getFullYear() - 12)
                        return date <= twelveYearsAgo
                    }, 'You must be at least 12 years old to register')
                    .transform((dateStr) => new Date(dateStr)),
                levelId: z.number().min(1, 'Please select your academic level')
            })
        }

        const result = schema.safeParse(body)
        if (!result.success) {
            return c.json({
                error: 'Invalid input',
                details: result.error.issues.map(issue => ({
                    field: issue.path.join('.'),
                    message: issue.message
                }))
            }, 400)
        }

        const profileData = result.data

        // For students, verify that the levelId exists
        if (user.type === 'student' && 'levelId' in profileData) {
            const levelExists = await prisma.level.findUnique({
                where: { id: profileData.levelId as number }
            })

            if (!levelExists) {
                return c.json({
                    error: 'Invalid level selection'
                }, 400)
            }
        }

        // Update user profile and mark email as verified
        const updateData: {
            firstName: string
            lastName: string
            gender: string
            emailVerified: boolean
            verifyKey: null
            verifyKeyDate: null
            birthDate?: Date
            levelId?: number
        } = {
            firstName: profileData.firstName,
            lastName: profileData.lastName,
            gender: profileData.gender,
            emailVerified: true,
            verifyKey: null,
            verifyKeyDate: null
        }

        if (user.type === 'student' && 'birthDate' in profileData && 'levelId' in profileData) {
            updateData.birthDate = profileData.birthDate as Date
            updateData.levelId = profileData.levelId as number
        }

        await prisma.user.update({
            where: { id: user.id },
            data: updateData
        })

        logger.info({
            userId: user.id,
            email: user.email,
            type: user.type
        }, 'Profile completed successfully')

        return c.json({
            success: true,
            message: 'Profile completed successfully! You can now login.',
            email: user.email
        })

    } catch (error) {
        logger.error({ error }, 'Profile completion error')
        return c.json({
            error: 'Failed to complete profile'
        }, 500)
    }
})

export default authRoutes
