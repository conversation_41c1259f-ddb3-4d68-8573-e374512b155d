{"main": {"placeholder": "Select an appreciation", "general": "General appreciation", "domain": "Subject appreciation"}, "list": {"title": "List Appreciations", "new": "new appreciation", "appresiation": "appreciation", "view": "view", "today": "Today", "yesterday": "Yesterday"}, "appreciation_domain": {"form": {"criteria": {"written_tests": "Written tests", "practical_work": "Practical work", "oral_participation": "Oral participation", "group_projects": "Group projects", "homework": "Homework"}, "title": "Request customization", "return": "return", "lang": "Language choice", "lang_placeholder": "select language", "fr": "French", "en": "English", "ar": "Arabic", "year": "Year", "trimester": "Trimester", "trimester_placeholder": "select trimester", "first": "first trimester", "second": "second trimester", "third": "third trimester", "domain_choise": "Subject choice", "length": "Appreciation length", "characters": "(characters)", "ton": "Remark tone", "critical": "Critical", "caring": "Caring", "skills": "Evaluated skills (4 max)", "skills_placeholder": "choose skills", "criteria_label": "Evaluation criteria (3 max)", "criteria_placeholder": "choose criteria", "export_import": "Model export and import (optional)", "export_description": "Fill in and import the model spreadsheet, DinoBot takes care of the rest", "download": "Download the model", "next": "Next", "title_btn": "Customize appreciation generation", "title_dialog": "Appreciation generation customization", "save": "Save"}, "next": {"return": "return", "class_moy": "Class average", "traitment": "Processing", "generate": "Generate"}, "item": {"progress": "Progress", "behavior": "Behavior", "investisment": "Investment", "custom": "Custom advice", "max": "max characters", "domain_moy": "Subject average"}, "result": {"class_moy": "Class average", "export": "Export all", "regenere": "Regenerate all", "generate": "Generate other appreciations", "error": "An error occurred!", "error_download": "An error occurred! Unable to download the file"}, "item_result": {"custom": "Custom advice", "max": "max characters"}, "dialog_form_student": {"btn_title": "Customize this generation", "title": "Appreciation generation customization", "point_area": "Strengths and areas for improvement", "point_area_placeholder": "Write here...", "max_point": "characters", "progress": "Progress and attitude", "progress_placeholder": "Write here...", "max_progress": "characters", "recomand": "Custom recommendation", "recomand_placeholder": "Write here...", "max_recomand": "characters", "point": "Main point to improve", "point_placeholder": "Deepening knowledge", "level": "Student level", "level_placeholder": "Excellent", "strong_point": "Specific strengths (2 max)", "strong_point_placeholder": "choose", "attitude": "Class attitude", "attitude_placeholder": "Involved and leading", "ton": "Remark tone", "critical": "Critical", "caring": "Caring", "save": "Save"}}, "appreciation_general": {"form": {"criteria": {"written_tests": "Written tests", "practical_work": "Practical work", "oral_participation": "Oral participation", "group_projects": "Group projects", "homework": "Homework"}, "title": "Request customization", "return": "return", "lang": "Language choice", "lang_placeholder": "select language", "fr": "French", "en": "English", "ar": "Arabic", "year": "Year", "trimester": "Trimester", "trimester_placeholder": "select trimester", "first": "first trimester", "second": "second trimester", "third": "third trimester", "domain_choise": "Subject choice", "length": "Appreciation length", "characters": "(characters)", "ton": "Remark tone", "critical": "Critical", "caring": "Caring", "skills": "Evaluated skills (4 max)", "skills_placeholder": "choose skills", "criteria_label": "Evaluation criteria (3 max)", "criteria_placeholder": "choose criteria", "export_import": "Model export and import (optional)", "export_description": "Fill in and import the model spreadsheet, DinoBot takes care of the rest", "download": "Download the model", "next": "Next", "title_btn": "Customize generation", "title_dialog": "Appreciation generation customization", "save": "Save"}, "init": {"return": "return", "title": "Enter one or more reports to start", "step1": "Step 1: Customize the request (optional)", "step2": "Step 2: Import your reports", "import": "Report import", "loading": "Processing", "next": "Next"}, "result": {"export": "Export all", "regenere": "Regenerate all", "generate": "Generate other appreciations", "error": "An error occurred!", "error_download": "An error occurred! Unable to download the file"}, "item": {"bultin": "Reference report", "custom": "Custom advice", "max_custom": "max characters", "regenerate": "Regenerate"}, "dialog_form_student": {"btn_title": "Customize this generation", "title": "Appreciation generation customization", "domain_chois": "Subject choice", "ton_app": "Appreciation tone", "ton_app_placeholder": "Excellent", "ton": "Remark tone", "critical": "Critical", "caring": "Caring", "save": "Save"}}}