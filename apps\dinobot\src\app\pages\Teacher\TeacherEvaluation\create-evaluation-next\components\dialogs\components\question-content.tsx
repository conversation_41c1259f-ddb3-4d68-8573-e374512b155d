import { Button } from '@dinobot/components-ui'
import { ScrollArea } from '@dinobot/components-ui'
import { useTranslation } from 'react-i18next'
import React, { useCallback, useState } from 'react'
import QuestionCard from './question-card'
import { Loader2, Plus } from 'lucide-react'
import {
    selectEvaluationParamsStore,
    useEvaluationParamsStore
} from '../../../../store/evaluation-params.store'
import { useGenerateQuestions } from '../../../../hooks/useGenerateQuestions'
import { JsonValueType } from '@dinobot/prisma'

export type QuestionCardType = {
    id: number
    knowledge: string
    question: string
    solution: string
    competences: string[]
    questionNbr: number
}

type QuestionContentProps = {
    isLoading: boolean
    exoIndex: number
    quesNum: number
    onClose: () => void
    questions: QuestionCardType[] | undefined
}

const QuestionContent = ({
    isLoading,
    questions,
    exoIndex,
    onClose,
    quesNum
}: QuestionContentProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/db')
    const { part, domain, level, chapter, addQuestions, exos, resetFromDb } =
        useEvaluationParamsStore()

    const generateQuestionsMutation = useGenerateQuestions()
    const [listQst, setList] = useState<QuestionCardType[]>([])
    const setStatement = selectEvaluationParamsStore.use.setStatement()

    /**
     * Handles adding a question to the list
     * - If question already exists, increases its quantity
     * - If not, adds it as a new entry with quantity of 1
     */
    const handleAddQst = useCallback(
        (qst: QuestionCardType) => {
            const listClone = structuredClone(listQst)

            const index = listClone.findIndex(q => q.id === qst.id)
            if (index !== -1) {
                listClone[index].questionNbr = listClone[index].questionNbr! + 1
                setList([...listClone])
            } else setList([...listClone, { ...qst, questionNbr: 1 }])
        },
        [listQst]
    )

    /**
     * Handles removing a question from the list
     * - If quantity is 1, removes completely
     * - If quantity > 1, decrements the quantity
     */
    const handleRemoveQst = useCallback(
        (qst: QuestionCardType) => {
            // Create a deep clone of the current list
            const list = structuredClone(listQst)
            const index = list.findIndex(q => q.id === qst.id)
            if (index !== -1) {
                if (list[index].questionNbr === 1) {
                    setList(list.filter(q => q.id !== qst.id))
                    return
                }
                list[index].questionNbr = list[index].questionNbr! - 1
                setList([...list])
                return
            } else setList(list)
        },
        [listQst]
    )

    /**
     * Handles submitting the selected questions to generate a new exercise
     * Uses the AI generation API to create questions based on the selected templates
     */
    const addExo = () => {
        const list = listQst.map(q => ({
            knowledge: q.knowledge,
            question: q.question,
            questionNbr: q.questionNbr,
            solution: q.solution,
            competences: q.competences,
            statement: exos[exoIndex].statement!,
            exerciseType: exos[exoIndex].questionsType,
            previousQuestions: exos[exoIndex].questions?.map(q => ({
                knowledge: q.content!,
                question: q.content!,
                solution: q.solution!,
                statement: ''
            }))
        }))

        generateQuestionsMutation.mutate({
            input: list,
            level: level!,
            domain: domain!,
            chapter: chapter!,
            part,
            //@ts-expect-error Explanation: problem type of exos with generate type
            evalType: exos[exoIndex].questionsType
        }, {
            onSuccess: (ques) => {
                if (!ques) return
                const questions = ques.output.map(que => ({
                    content: que?.question,
                    desmosCode: que.desmosCode as JsonValueType,
                    solution: que.solution
                }))
                addQuestions(exoIndex, quesNum, [...questions!])
                if (exos[exoIndex].hasStatment)
                    setStatement(exoIndex, ques.statement)
                setList([])
                resetFromDb()
                onClose()
            },
            onError: (error) => {
                console.error('Error generating questions:', error)
            }
        })
    }
    return (
        <div className="w-full p-4 flex flex-col gap-2">
            <div className="flex justify-between items-center bg-dinoBotBlue/20 p-1 rounded-sm">
                <span>{t('exo')} 1</span>
                <Button
                    className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue px-2"
                    onClick={addExo}
                    disabled={generateQuestionsMutation.isPending || listQst.length === 0}
                >
                    {generateQuestionsMutation.isPending ? (
                        <>
                            <Loader2 className="mr-2 size-4 animate-spin" />
                            {t('loading')}
                        </>
                    ) : (
                        <>
                            <Plus />
                            {t('add_exo')}
                        </>
                    )}
                </Button>
            </div>
            {!isLoading ? (
                <ScrollArea className="h-[420px] 2xl:h-[630px]">
                    <div className="grid grid-cols-2 grid-rows-5 gap-3 w-fit">
                        {questions ? (
                            questions.map((qst, i) => (
                                <QuestionCard
                                    key={i}
                                    qst={{
                                        ...qst,
                                        questionNbr: 0,
                                        id: i,
                                        solution: qst.solution!
                                    }}
                                    listQst={listQst}
                                    onAddClick={handleAddQst}
                                    onRemoveClick={handleRemoveQst}
                                />
                            ))
                        ) : (
                            <div className="flex justify-center items-center size-full">
                                {t('no_question')}
                            </div>
                        )}
                    </div>
                </ScrollArea>
            ) : (
                <Loader2 className="mx-auto animate-spin" />
            )}
        </div>
    )
}

export default QuestionContent
