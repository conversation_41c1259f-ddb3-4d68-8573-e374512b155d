import { StudentAnswerPartial, SubmissionStateType } from '@dinobot/prisma'
import { z } from 'zod'

// Schémas de validation pour les requêtes
export const CreatePlannedEvaluationSchema = z.object({
    classId: z.string(),
    controlId: z.string(),
    availableDate: z.string().datetime(),
    dueDate: z.string().datetime(),
    description: z.string(),
    title: z.string(),
    retries: z.number().int().min(0),
    timeLimit: z.number().int().min(0),
    control: z.object({
        status: z.string().optional()
    }).optional()
})

export const RemovePlannedEvaluationSchema = z.object({
    id: z.string()
})

export const GetPlannedEvaluationsSchema = z.object({
    classId: z.string()
})

export const GetStudentSubmissionSchema = z.object({
    plannedEvaluationId: z.string()
})

export const GetStudentsByPlannedEvaluationSchema = z.object({
    plannedEvaluationId: z.string()
})

export const GetStudentsByClassSchema = z.object({
    classId: z.string()
})

export const SubmitNotationSchema = z.object({
    id: z.string().optional(),
    answers: z.array(z.object({
        id: z.string().optional(),
        feedback: z.string().optional(),
        score: z.number().optional()
    })).optional(),
    globalScore: z.number(),
    globalFeedback: z.string(),
    submissionState: z.string(),
    controlId: z.string().optional(),
    studentId: z.string().optional()
})

export const GiveBackCopySchema = z.object({
    plannedEvaluationId: z.string()
})

export const GetNotationByClassSchema = z.object({
    classId: z.string(),
    order: z.enum(['asc', 'desc']).optional()
})

export const GetNotationMinMaxSchema = z.object({
    controlId: z.string()
})

// Types pour les réponses
export type CreatePlannedEvaluationRequest = z.infer<typeof CreatePlannedEvaluationSchema>
export type RemovePlannedEvaluationRequest = z.infer<typeof RemovePlannedEvaluationSchema>
export type GetPlannedEvaluationsRequest = z.infer<typeof GetPlannedEvaluationsSchema>
export type GetStudentSubmissionRequest = z.infer<typeof GetStudentSubmissionSchema>
export type GetStudentsByPlannedEvaluationRequest = z.infer<typeof GetStudentsByPlannedEvaluationSchema>
export type GetStudentsByClassRequest = z.infer<typeof GetStudentsByClassSchema>
export type SubmitNotationRequest = z.infer<typeof SubmitNotationSchema>
export type GiveBackCopyRequest = z.infer<typeof GiveBackCopySchema>
export type GetNotationByClassRequest = z.infer<typeof GetNotationByClassSchema>
export type GetNotationMinMaxRequest = z.infer<typeof GetNotationMinMaxSchema>

export type NotationSubmissionType = {
  id: string;
  answers: StudentAnswerPartial[];
  globalScore: number;
  globalFeedback: string;
  submissionState: SubmissionStateType;
  controlId?: string;
  studentId?: string;
};

export type PlannedEvaluationPartialWithRelations = {
    id?: string
    classId?: string
    controlId?: string
    availableDate?: Date
    dueDate?: Date
    description?: string
    title?: string
    retries?: number
    timeLimit?: number
    isCorrected?: boolean
    submitCorrectedAt?: Date
    control?: {
        status?: string
    }
}
