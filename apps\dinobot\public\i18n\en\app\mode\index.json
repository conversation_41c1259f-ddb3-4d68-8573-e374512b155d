{"controle": {"timer": "Time remaining", "answerControl": {"successMessage": "Your answers have been successfully submitted, please check your copy", "errorMessage": "An error occurred while submitting your answers", "saveAllResponsePlease": "Please save all your responses before submitting.", "placeholder": "Your answer...", "addMathFormula": "Add a mathematical formula", "editAnswer": "Edit the answer", "saveAnswer": "Save the answer", "submitAnswer": "Submit my answers"}, "controlPreview": {"controlSubject": "Control subject", "myCopy": "My copy", "correction": "Correction", "controlOf": "Control of", "noAnswer": "No answer.", "copyExercise": "Copy the exercise", "printExercise": "Print the exercise", "noControle": "no control", "errorOccurred": "Sorry, something went wrong while preparing the exam.", "imageNotFound": "Sorry, we could not display the image.", "failedToDownload": "Sorry, we couldn’t download the exercise file."}, "prof_review": {"title": "Summary of ", "name": "Student's First and Last Name", "date": "Date of the Evaluation", "du": "From", "au": "To", "type": "Type of the Evaluation", "note": "Grade Obtained"}}, "create-controle": {"title": "Create Control", "chapter": {"title": "Chapter", "placeholder": "Select a chapter"}, "duration": "Duration", "submit": "Create", "tinfo": "Please select a chapter and set a duration", "loading": "Loading...", "error": {"fetchChapters": "Error loading chapters", "fetchParts": "Error loading parts", "validation": "Please check your selections"}}, "exo": {"level": {"title": "Choose a class"}, "module": {"chois": "Select a Subject", "math": "Mathematics", "fr": "French", "physique-chimie": "Physics-Chemistry", "svt": "Life and Earth Sciences", "emc": "Civic and Moral Education", "geo": "Geography", "history": "History"}, "tab": {"title": "Generate an exercise", "secondTitle": "How would you like to create?", "db": {"title": "Database", "tinfo": {"n10": "The number of questions cannot be greater than 10!", "n1": "The number of questions cannot be less than 1!", "info": "You must fill in the required fields!", "error": "The number of questions must be between 1 and 10!"}, "chois": {"title": "Select a Chapter", "placeholder": "Choose a Chapter"}, "subject": {"name": "Subject", "title": "Choose a Subject", "placeholder": "Select a Subject", "error-chois": "You must select a Subject"}, "error-chois": "You must choose a chapter", "chois-partie": "Select a part", "error-chois-partie": "You must choose a part", "part": {"title": "Part", "info": "Choose a part", "error": "You must choose a part", "placeholder": "Choose a part"}, "questions-lenght": "Number of exercises", "exonumber": "Number of exercises", "questions-number": "Number of questions", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom-prompt": "Customize with a prompt", "opt": "(optional)", "detail": "Explain your needs to DinoBot, which will generate a custom exercise for you.", "oublie": "Remember: your request must be clear and precise.", "redigez": "Write here...", "caracteres": "characters", "submit": "Generate"}, "file": {"title": "From a file", "error": "You must upload a statement in PDF format!", "generate": "Generate from a file", "chois": "Choose a chapter", "download": "Upload your file to DinoBot, which will extract the content to create custom exercises.", "rappelle": "Remember: your file must be readable and relevant to the study topic.", "enonce": "Statement", "Remplacez le fichier": "Replace the file", "Téléchargez un fichier": "Upload a file", "correction": "Correction (optional)", "max": "the file must be in .pdf format, maximum 100 MB", "replace": "Replace the file", "upload": "Upload a file", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prompt": "Customize with a prompt", "opt": "(optional)", "exp": "Explain your needs to DinoBot, which will generate a custom exercise for you.", "noubliep": "Remember: your request must be clear and precise.", "rid": "Write here...", "caracteres": "characters", "questions": "You cannot generate more than 10 questions.", "submit": "Generate"}, "exam": {"title": "From an exam", "toast": {"n10": "The number of questions cannot be greater than 10!", "n1": "The number of questions cannot be less than 1!", "n1&10": "The number of questions must be between 1 and 10!", "fill": "You must fill in the required fields!"}, "table": {"title": "Title", "nofill": "Not specified", "year": "Year"}, "chapitre": "Chapter", "chois": "Choose a chapter", "chois-error": "You must choose a chapter", "exam": "Exam", "chois-savoir": "Choose a skill", "error-chois-savoir": "You must choose a skill", "chois-savoir-text": "Choose a skill proposed by <PERSON><PERSON><PERSON> and get custom training exercises!", "result": "results matching your criteria", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "have": "You cannot generate more than 10 questions.", "prompt": "Customize with a prompt", "opt": "(optional)", "besoins": "Explain your needs to DinoBot, which will generate a custom exercise for you.", "noubliep": "Remember: your request must be clear and precise.", "rid": "Write here...", "caracteres": "characters", "submit": "Generate"}}, "mini-tabs": {"file": {"generate": "Generate from a file", "chois-chap": "Choose a chapter", "download": "Upload your file to DinoBot, and it will extract the content to create tailored exercises.", "rappelle": "Remember: your file must be readable and relevant to the study topic.", "enonce": "Statement", "remp-file": "Replace the file", "down-file": "Upload a file", "correction": "Correction (optional)", "max": "The file must be in .pdf format, maximum 100 MB", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "Generate", "error": "You must upload a statement in PDF format!"}, "db": {"chois": "Choose a chapter", "chap": "Chapter", "part": "Part", "error-chois": "You must choose a chapter", "chois-part": "Choose a part", "error-chois-part": "You must choose a part", "nbr": "Number of exercises", "exonumber": "Number of exercises", "questions-number": "Number of questions", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "n10": "The number of questions cannot exceed 10!", "n1": "The number of questions cannot be less than 1!", "n1&10": "The number of questions must be between 1 and 10!", "error": "You must fill in the required fields!", "submit": "Generate"}, "exam": {"chap": "Chapter", "chois-chap": "Choose a chapter", "error-choi-chap": "You must choose a chapter", "exam": "Exam", "chois-sav": "Choose a knowledge", "error-chois-sav": "You must choose a knowledge", "chois-sav-prps": "Choose a knowledge proposed by <PERSON><PERSON><PERSON> and get personalized training exercises!", "res": "results matching your criteria", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "np10": "You cannot generate more than 10 questions.", "n10": "The number of questions cannot exceed 10!", "n1": "The number of questions cannot be less than 1!", "n1&10": "The number of questions must be between 1 and 10!", "error": "You must fill in the required fields!", "submit": "Generate", "table": {"vide": "No exercise found.", "no-write": "Not provided"}}}}, "train": {"exo": "Exercise", "copyexo": "Copy the exercise", "regexo": "Regenerate the exercise", "question": "Question", "printexo": "Print the exercise", "creation": "Your exercise is being created...", "continue": "Log in to continue discussing together!", "env": {"part1": "The environment variable is missing!", "part2": "is missing!"}, "error": "An unexpected error occurred while generating the exercise", "beta": {"message": "You are currently experiencing a beta function", "description": "Your feedback is precise for us to improve. Thank you for your help and your contribution! 🙏"}}}