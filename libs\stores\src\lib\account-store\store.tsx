import { CustomPrompts } from '@dinobot/utils'
import { create } from 'zustand'
import { createSelectors } from '../selectors.store'
import {
    User,
    UserWithPartialRelations
} from '@dinobot/prisma'
import { UserStatusSchema} from '@dinobot/prisma'


interface AccountState {
    user: UserWithPartialRelations
    isAccountPopupOpen: boolean
    isEditingProfile: boolean
    setUser: (value: User) => void
    setOpenAccountPopup: (open: boolean) => void
    setIsEditingProfile: (value: boolean) => void
}

const useAccountStore = create<AccountState>((set, get) => ({
    user: {
        id: '',
        email: '',
        password: '',
        salt: '',
        userLevel: null,
        firstName: null,
        lastName: null,
        gender: null,
        birthDate: null,
        level: null,
        type: 'student',
        phoneNumber: null,
        verifyKey: null,
        verifyKeyDate: null,
        hasSelectLevelDomain: false,
        emailVerified: false,
        isAdmin: false,
        resetKey: null,
        resetKeyDate: null,
        deletionRequestedAt: null,
        scheduledDeletionAt: null,
        status: UserStatusSchema.enum.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date(),
        levelId: null,
        maxDomains: 2,
        maxLevelDomains: 4
    },
    userPrompts: null,
    isAccountPopupOpen: false,
    isEditingProfile: false,
    setUser: value => set({ user: value }),
    setOpenAccountPopup: open => set({ isAccountPopupOpen: open }),
    setIsEditingProfile: value => set({ isEditingProfile: value }),
}))

export default useAccountStore
export const selectAccountStore = createSelectors(useAccountStore)
