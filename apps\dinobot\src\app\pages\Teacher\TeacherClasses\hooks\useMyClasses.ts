// apps/dinobot/src/app/pages/Teacher/TeacherClasses/hooks/useMyClasses.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuthApiClient } from '../../../../contexts/AppContext';
import { Class, ClassInput } from '../my-classes.types';

// Type pour les données domain/level
interface DomainLevelData {
  levelDomainId: string;
  levelDomain: {
    domainId: number;
    domain: {
      id: number;
      name?: string;
      nameEn?: string;
      nameAr?: string;
    };
    level: {
      id: number;
      name?: string;
      nameEn?: string;
      nameAr?: string;
    };
  };
}

export function useMyClasses() {
  const authApiClient = useAuthApiClient();
  const queryClient = useQueryClient();

  const useArchivedClassesQuery = (year: string) => {
    return useQuery({
      queryKey: ['classes-archived', year],
      queryFn: async (): Promise<Class[]> => {
        const response = await authApiClient.get(
          `/api/classes?schoolYear=${year}&status=archived`,
        );
        return response.data;
      },
      staleTime: 5 * 60 * 1000,
      enabled: !!year, // N'exécute la query que si year est défini
    });
  };

  // Query pour récupérer toutes les classes
  const {
    data: classesData,
    isLoading: isLoadingClasses,
    error: classesError,
    refetch: refetchClasses,
  } = useQuery({
    queryKey: ['classes'],
    queryFn: async (): Promise<Class[]> => {
      const response = await authApiClient.get('/api/classes');
      return response.data;
    },
    staleTime: 5 * 60 * 1000,
  });

  // ✅ Query pour récupérer les domaines et niveaux
  const {
    data: domainLevelData,
    isLoading: isLoadingDomainLevel,
    error: domainLevelError,
  } = useQuery({
    queryKey: ['domains'],
    queryFn: async (): Promise<DomainLevelData[]> => {
      const response = await authApiClient.get(
        '/api/domain-level/user-level-domains',
      );
      return response.data;
    },
    staleTime: 10 * 60 * 1000,
  });

  // Fonction pour récupérer les classes (pour compatibilité)
  const getClassesQuery = async (): Promise<Class[]> => {
    const response = await authApiClient.get('/api/classes');
    return response.data;
  };

  // ✅ Mutation pour créer une classe
  const createClassMutation = useMutation({
    mutationFn: async (data: ClassInput): Promise<Class> => {
      const response = await authApiClient.post('/api/classes', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
    },
  });

  // Mutation pour mettre à jour une classe
  const updateClassMutation = useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: ClassInput;
    }): Promise<Class> => {
      const response = await authApiClient.put(`/api/classes/${id}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
    },
    onError: (error) => {
      console.error('Error updating class:', error);
    },
  });

  // Mutation pour supprimer une classe
  const deleteClassMutation = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await authApiClient.delete(`/api/classes/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
    },
  });

  // Mutation pour archiver une classe
  const archiveClassMutation = useMutation({
    mutationFn: async (id: string): Promise<Class> => {
      const response = await authApiClient.post(`/api/classes/${id}/archive`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['classes'] });
    },
  });

  return {
    // ✅ Fonction de query (pour compatibilité avec l'ancien code)
    getClassesQuery,

    // ✅ Données des classes
    classesData,
    isLoadingClasses,
    classesError,
    refetchClasses,

    // ✅ Données des domaines/niveaux (AJOUTÉ)
    domainLevelData,
    isLoadingDomainLevel,
    domainLevelError,

    // ✅ Hook pour les classes archivées
    useArchivedClassesQuery,

    // ✅ Fonction createClass simplifiée (AJOUTÉ)
    createClass: createClassMutation.mutateAsync,
    isCreatingClass: createClassMutation.isPending,
    createClassError: createClassMutation.error,

    // ✅ Mutations complètes (pour compatibilité)
    createClassMutation,
    updateClassMutation,
    deleteClassMutation,
    archiveClassMutation,

    // Fonctions de mutation simplifiées
    updateClass: updateClassMutation.mutateAsync,
    isUpdatingClass: updateClassMutation.isPending,
    updateClassError: updateClassMutation.error,

    deleteClass: deleteClassMutation.mutateAsync,
    isDeletingClass: deleteClassMutation.isPending,
    deleteClassError: deleteClassMutation.error,

    archiveClass: archiveClassMutation.mutateAsync,
    isArchivingClass: archiveClassMutation.isPending,
    archiveClassError: archiveClassMutation.error,
  };
}
