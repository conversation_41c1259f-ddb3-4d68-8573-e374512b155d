import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@dinobot/components-ui'
import { getLangProps } from '@dinobot/utils'
import { cn } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import React from 'react'
import { useChaptersByDomainAndLevel } from '../../../../hooks/useChaptersByDomainAndLevel'
import { usePartsByChapter } from '../../../../hooks/usePartsByChapter'
import { useEvaluationParamsStore } from '../../../../store/evaluation-params.store'
import { Chapter, Part } from '@dinobot/prisma'

const ChapterListPart = () => {
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)
    const {
        domain,
        level,
        chapter: selectedChapter,
        setChapter,
        setPart
    } = useEvaluationParamsStore()

    const { data: chapters } = useChaptersByDomainAndLevel(domain?.id , level?.id)
    const { data: parts } = usePartsByChapter(selectedChapter?.id)
    return (
        <Accordion type="single" collapsible>
            {chapters?.map((chapter: Chapter) => (
                <AccordionItem
                    key={chapter.id}
                    value={chapter.id}
                    className="border-none"
                >
                    <AccordionTrigger
                        className={cn(
                            'flex-row-reverse justify-end gap-2 truncate'
                        )}
                        style={{ direction: dir }}
                        onClick={() => setChapter(chapter)}
                    >
                        {getLangProps({
                            obj: chapter,
                            base: 'title',
                            lang: i18n.language
                        })}
                    </AccordionTrigger>
                    <AccordionContent className="flex flex-col gap-2">
                        {parts?.map((part: Part) => (
                            <div
                                className="ml-12 cursor-pointer truncate"
                                key={part.id}
                                onClick={() => setPart(part)}
                                dir={dir}
                            >
                                {getLangProps({
                                    obj: part,
                                    base: 'name',
                                    lang: i18n.language
                                })}
                            </div>
                        ))}
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    )
}
export default ChapterListPart
