// src/pages/teacher/my-classes/components/ApropoForm.tsx
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useApropoStore } from '../../stores/AboutTab.store';
import { useAboutTab } from '../../hooks/useAboutTab';
import { Accordion, Button } from '@dinobot/components-ui';
import { Plus, QrCode } from 'lucide-react';
import { CodeDialog } from './CodeDialog';
import ThemeBlock from './ThemeBlock';

const ApropoForm: React.FC = () => {
  const { t } = useTranslation([
    'teacher/my-class/classes',
    'teacher/my-class/apropo',
  ]);
  const { classId } = useParams<{ classId: string }>();
  const {
    setClass,
    setThemes,
    classes,
    themes,
    setSelectedThemeId,
    selectedThemeId,
  } = useApropoStore();
  const { useClassQuery, useThemesQuery, useControlsByThemeIdQuery } =
    useAboutTab(classId || '');

  const classQuery = useClassQuery();
  const themesQuery = useThemesQuery();
  const controlsQuery = useControlsByThemeIdQuery(selectedThemeId);

  useEffect(() => {
    if (classQuery.data) {
      setClass(classQuery.data);
    }
  }, [classQuery.data, setClass]);

  useEffect(() => {
    if (themesQuery.data) {
      setThemes(themesQuery.data);
    }
  }, [themesQuery.data, setThemes]);

  const handleAccordionChange = (value: string) => {
    // Set the selected theme ID when accordion is opened
    setSelectedThemeId(value === "" ? null : value);
  };

  if (classQuery.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-dinoBotRedOrange" />
      </div>
    );
  }

  return (
    <div className="w-full h-full p-6 bg-gray-50">
      <div className="flex justify-end mb-1 underline">
        <Button variant="link" className="text-blue-600">
          {t('editClassInfo')}
        </Button>
      </div>

      {/* Class Info Card */}
      <div
        className={`rounded-lg p-6 mb-6 text-white relative`}
        style={{ background: classes?.classColor }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Class Avatar */}
            <div className="w-fit h-16 bg-white rounded-lg p-3 flex items-center justify-center">
              <span className="text-md font-bold text-black">
                {classes?.name}
              </span>
            </div>

            {/* Class Details */}
            <div>
              <div className="text-md text-black opacity-90">
                {classes?.students.length}{' '}
                {t('details.students', { ns: 'teacher/my-class/apropo' })}
              </div>
              <div className="text-md font-semibold text-dinoBotDarkGray">
                {t('add.teacher_main')} :
              </div>
              <div className="text-lg font-semibold text-black">
                {classes?.mainTeacher?.firstName}{' '}
                {classes?.mainTeacher?.lastName}
              </div>
            </div>
          </div>

          {/* Class Code Button */}
          <CodeDialog
            trigger={
              <>
                <QrCode />{' '}
                {t('details.qrcode', { ns: 'teacher/my-class/apropo' })}
              </>
            }
          />
        </div>
      </div>

      {/* Create Button */}
      <div className="mb-6">
        <Button className="bg-green-500 hover:bg-green-600 text-white rounded-full px-6 py-2">
          <Plus className="mr-2 h-4 w-4" />
          {t('create')}
        </Button>
      </div>

      {/* Themes and Evaluations Section */}
      {themes && themes.length > 0 && (
        <div className="flex flex-col gap-2">
          <Accordion
            type="single"
            collapsible
            className="flex flex-col gap-2 w-full"
            onValueChange={handleAccordionChange}
            value={selectedThemeId || ""}
          >
            {themes.map(
              (theme) =>
                theme && (
                  <ThemeBlock
                    key={theme.id}
                    theme={theme}
                    isLoading={
                      selectedThemeId === theme.id && controlsQuery.isLoading
                    }
                    controls={
                      selectedThemeId === theme.id
                        ? controlsQuery.data
                        : undefined
                    }
                  />
                ),
            )}
          </Accordion>
        </div>
      )}
    </div>
  );
};

export default ApropoForm;
