import { Hono, Context } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { streamText as aiStreamText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { nanoid } from 'nanoid';
import { logger } from '@/lib/logger';
import { tracedStreamText } from '@/lib/telemetry/ai-wrapper';
import { registry } from '@/lib/ai/config-ai';
import { fetchProviderAndModelByTask } from '@/lib/ai/llm/server';
import { SystemPromptService, PromptFeatureType } from '@/lib/ai/prompts/system-prompt-service';
import { buildConditionalPrompt } from '@/lib/ai/prompts/chat/chat-prompts';
import { getCurrentLLM } from '@/lib/ai/llm/generator';
import { requireAuth, getAuthUser, getAuthContext } from '@/lib/auth/middleware';
import {
  checkChatExistence,
  createNewChat,
  updateExistingChat,
  sendFileToMathpixOcr,
  sendFileToMistralOcr,
  getRandomErrorMessage,
  getChats,
  getChat,
  removeChat,
  clearChats,
  updateChatTitle,
  getFiles,
  getFile,
  removeFile,
  pinChat,
  unpinChat,
  checkMessagesAccess,
  checkFilesAccess,
  MessageWithFile,
  ChatWithMessages
} from './actions';
import { getFullUserById, getUserById } from '../auth/auth';
import { stream } from 'hono/streaming';

// Request validation schema
const chatRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string(),
    id: z.string().optional(),
    createdAt: z.date().optional(),
  })),
  id: z.string(),
  imageFile: z.string().optional(),
  fileExtension: z.string().optional(),
  exercise: z.any().optional(),
  currentPDF: z.object({
    assignment: z.string().optional()
  }).optional(),
});

export type ChatRequest = z.infer<typeof chatRequestSchema>;

// Main chat endpoint - streaming response
const chatHandlerImpl = async (c: Context) => {
    try {
      const body: any = await c.req.json();

      const { messages, id, imageFile, fileExtension, exercise, currentPDF } =
        body;

      // Get locale from query params
      const locale = c.req.query('locale') || 'fr';

      // Get authenticated user
      const authContext = getAuthContext(c);
      const user = authContext?.user || null;

      // Get cookies for feature, topic, mode
      const feature = c.req.header('Cookie')?.match(/feature=([^;]+)/)?.[1];
      const topic = c.req.header('Cookie')?.match(/topic=([^;]+)/)?.[1];
      const mode = c.req.header('Cookie')?.match(/mode=([^;]+)/)?.[1];
      const currentExerciseId = c.req
        .header('Cookie')
        ?.match(/exerciseId=([^;]+)/)?.[1];

      const userMessageCreatedAt = new Date();
      const path = `/chat/${id}`;

      // 1. Check chat existence
      const existingChat = await checkChatExistence(path);

      // 2. Get system prompts
      const chatPrompts = await SystemPromptService.getSystemPrompt(
        PromptFeatureType.CHAT,
      );

      // 3. Process OCR if image file exists
      let mathpixOcrText = '';
      let fileBase64 = '';
      const enhancedMessages = [...messages];

      if (imageFile && fileExtension) {
        try {
          logger.info('Processing file with OCR');
          const documentOcr = process.env.DOCUMENT_OCR ?? 'mistral';
          const mathpixResult =
            documentOcr === 'mistral'
              ? await sendFileToMistralOcr(imageFile, fileExtension)
              : await sendFileToMathpixOcr(imageFile, fileExtension);

          fileBase64 = mathpixResult.fileBase64;
          mathpixOcrText = mathpixResult.text;

          // Add OCR text to the last user message
          if (mathpixOcrText && enhancedMessages.length > 0) {
            const lastUserMessageIndex = enhancedMessages.length - 1;
            const lastUserMessage = enhancedMessages[lastUserMessageIndex];

            if (lastUserMessage.role === 'user') {
              enhancedMessages[lastUserMessageIndex] = {
                ...lastUserMessage,
                content: `${lastUserMessage.content}\n\nContenu extrait du fichier:\n${mathpixOcrText}`,
              };
            }
          }
        } catch (error) {
          logger.error('Error processing file with OCR:', error);
        }
      }

      // 4. Get LLM provider and model
      const ELEVE_EXERCICE = 'Élève / Exercice';
      const ELEVE_CHAT = 'Élève / Chat';
      const ELEVE_EXAM = 'Élève / Examen';

      const providerAndModel = await fetchProviderAndModelByTask(
        feature === 'Chat'
          ? ELEVE_CHAT
          : feature === 'Exo'
            ? ELEVE_EXERCICE
            : ELEVE_EXAM,
      );

      // Default model setup
      const currentLLM = getCurrentLLM(mode || 'GPT 4-o');
      let actualModel = openai(currentLLM);

      // Use provider registry if available
      if (providerAndModel?.model && providerAndModel?.profile) {
        const reg = await registry();
        actualModel = reg.languageModel(
          // @ts-expect-error('error)
          `${providerAndModel.profile.name}:${providerAndModel.model}`,
        );
      }

      // 5. Build system prompt
      // @ts-expect-error('json-error')
      const baseSystemPrompt = chatPrompts?.mainPrompt?.prompt ?? '';
      const conditionalPrompt = await buildConditionalPrompt(
        feature,
        undefined,
        topic,
        currentPDF?.assignment,
        locale,
      );

      const systemPromptContent =
        `\n Réponds en utilisant la langue de l'utilisateur: ${locale} \n` +
        baseSystemPrompt +
        conditionalPrompt +
        (exercise
          ? `Voilà le contenu de l'exercice en question au format json: \n\n ${JSON.stringify(exercise)} \n\n`
          : '');

      // 6. Stream response with Langfuse tracing
      const result = await tracedStreamText({
        model: actualModel,
        messages: enhancedMessages,
        system: systemPromptContent,
        onFinish: async ({ text }) => {
          await saveMessages({
            messages,
            userMessageCreatedAt,
            id,
            currentExerciseId,
            existingChat,
            feature,
            topic,
            imageFile,
            fileExtension,
            fileBase64,
            assistantResponse: text,
            userId: user?.id || null,
          });
        },
      }, {
        traceName: `[${feature}]streamText`,
        userId: user?.email ?? 'unknown',
        sessionId: id,
        metadata: {
          tags: [feature],
          userId: user?.email ?? 'unknown',
        },
      });

      const randomErrorText = getRandomErrorMessage();

      // Mark the response as a v1 data stream:
      c.header('X-Vercel-AI-Data-Stream', 'v1');
      c.header('Content-Type', 'text/plain; charset=utf-8');

      return stream(c, (stream) => stream.pipe(result.toDataStream()));

      // return result.toDataStreamResponse({
      //   getErrorMessage: (error) => {
      //     logger.error(`Stream error: ${JSON.stringify(error)}`);
      //     return randomErrorText;
      //   },
      // });
    } catch (error) {
      logger.error(`Chat error: ${error}`);
      throw new HTTPException(500, {
        message: 'An error occurred while processing your request',
      });
    }
};


// Helper function to save messages after completion
async function saveMessages({
  messages,
  userMessageCreatedAt,
  id,
  currentExerciseId,
  existingChat,
  feature,
  topic,
  imageFile,
  fileExtension,
  fileBase64,
  assistantResponse,
  userId
}: {
  messages: any[]
  userMessageCreatedAt: Date
  id: string
  currentExerciseId?: string
  existingChat: ChatWithMessages | null
  feature?: string
  topic?: string
  imageFile?: string
  fileExtension?: string
  fileBase64?: string
  assistantResponse: string
  userId: string | null
}) {
  try {
    if (feature === 'Exo') return; // Skip saving for exercise mode

    // Get the last user message
    const lastUserMessageIndex = messages.map(m => m.role).lastIndexOf('user');
    const lastUserMessage = lastUserMessageIndex >= 0 ? messages[lastUserMessageIndex] : null;

    let messageWithFile: MessageWithFile | null = null;

    // Add file information if image was uploaded
    if (lastUserMessage?.role === 'user' && imageFile) {
      messageWithFile = {
        id: lastUserMessage.id || nanoid(),
        role: 'user',
        content: lastUserMessage.content,
        createdAt: lastUserMessage.createdAt || userMessageCreatedAt,
        file: {
          id: nanoid(),
          name: `${imageFile}-${Date.now()}.${fileExtension || 'png'}`,
          file: fileBase64 || '',
          createdAt: new Date()
        }
      };
    } else if (lastUserMessage) {
      messageWithFile = {
        id: lastUserMessage.id || nanoid(),
        role: 'user',
        content: lastUserMessage.content,
        createdAt: lastUserMessage.createdAt || userMessageCreatedAt
      };
    }

    // Create assistant response
    const assistantMessage: MessageWithFile = {
      id: nanoid(),
      role: 'assistant',
      content: assistantResponse,
      createdAt: new Date()
    };

    // Messages to store
    const messagesToStore = messageWithFile ? [messageWithFile, assistantMessage] : [assistantMessage];

    // Create title from user message
    let title = '';
    if (lastUserMessage?.content) {
      const content = typeof lastUserMessage.content === 'string' ? lastUserMessage.content : '';
      if (content.startsWith('msgllm4')) {
        const endDocIndex = content.indexOf('Ceci est la fin du document.');
        if (endDocIndex !== -1) {
          title = content.substring(endDocIndex + 'Ceci est la fin du document.'.length).substring(0, 100);
        } else {
          title = content.substring(0, 100);
        }
      } else {
        title = content.substring(0, 100);
      }
    }

    const processedTitle = title ? title.trim() : `Sans titre - ${topic || 'Chat'}`;

    const newChat = {
      id,
      title: processedTitle,
      userId: userId || 'anonymous',
      topic: topic || 'Mathématiques',
      path: `/chat/${id}`,
      messages: messagesToStore,
      exerciseId: feature === 'Exam' ? currentExerciseId : null
    };

    if (existingChat) {
      await updateExistingChat(existingChat, messagesToStore);
    } else {
      await createNewChat(newChat);
    }

    logger.debug(`Messages saved for chat ${id}`);
  } catch (error) {
    logger.error(`Error saving messages: ${error}`);
  }
}

// Route handlers for chat management
const getChatsHandler = async (c: Context) => {
  try {
    const user = getAuthUser(c);
    const chats = await getChats(user?.id);
    return c.json(chats);
  } catch (error) {
    logger.error(`Error fetching chats: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const getChatHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const user = getAuthUser(c);
    const chat = await getChat(id, user?.id);

    if (!chat) {
      throw new HTTPException(404, { message: 'Chat not found' });
    }

    return c.json(chat);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error fetching chat: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const removeChatHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await removeChat({
      id,
      path: `/chat/${id}`,
      userId: user.id
    });

    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error removing chat: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const clearChatsHandler = async (c: Context) => {
  try {
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await clearChats(user.id);
    return c.json(result);
  } catch (error) {
    logger.error(`Error clearing chats: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const updateChatTitleHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const { title } = await c.req.json();
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await updateChatTitle(id, title, user.id);

    if (!result) {
      throw new HTTPException(404, { message: 'Chat not found or unauthorized' });
    }

    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error updating chat title: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const getFilesHandler = async (c: Context) => {
  try {
    const user = getAuthUser(c);
    const files = await getFiles(user?.id);
    return c.json(files);
  } catch (error) {
    logger.error(`Error fetching files: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const getFileHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const file = await getFile(id);

    if (!file) {
      throw new HTTPException(404, { message: 'File not found' });
    }

    return c.json(file);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error fetching file: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const removeFileHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await removeFile(id, user.id);
    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error removing file: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const pinChatHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await pinChat(id, user.id);
    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error pinning chat: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const unpinChatHandler = async (c: Context) => {
  try {
    const { id } = c.req.param();
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const result = await unpinChat(id, user.id);
    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error unpinning chat: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const checkMessagesAccessHandler = async (c: Context) => {
  try {
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const fullUser = await getFullUserById(user?.id);

    const result = await checkMessagesAccess(fullUser, c);
    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error checking messages access: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

const checkFilesAccessHandler = async (c: Context) => {
  try {
    const user = getAuthUser(c);
    if (!user) throw new HTTPException(401, { message: 'Unauthorized' });

    const fullUser = await getFullUserById(user?.id);

    const result = await checkFilesAccess(fullUser, c);
    return c.json(result);
  } catch (error) {
    if (error instanceof HTTPException) throw error;
    logger.error(`Error checking files access: ${error}`);
    throw new HTTPException(500, { message: 'Internal Server Error' });
  }
};

// Create and export the Hono app with routes
const chatRoutes = new Hono();

// Chat streaming
chatRoutes.post('/', requireAuth, chatHandlerImpl);

// Chat management
chatRoutes.get('/chats', requireAuth, getChatsHandler);
chatRoutes.get('/chats/:id', requireAuth, getChatHandler);
chatRoutes.delete('/chats/:id', requireAuth, removeChatHandler);
chatRoutes.delete('/chats', requireAuth, clearChatsHandler);
chatRoutes.put('/chats/:id/title', requireAuth, updateChatTitleHandler);
chatRoutes.post('/chats/:id/pin', requireAuth, pinChatHandler);
chatRoutes.delete('/chats/:id/pin', requireAuth, unpinChatHandler);

// File management
chatRoutes.get('/files', requireAuth, getFilesHandler);
chatRoutes.get('/files/:id', requireAuth, getFileHandler);
chatRoutes.delete('/files/:id', requireAuth, removeFileHandler);

// Access checks
chatRoutes.get('/access/messages', requireAuth, checkMessagesAccessHandler);
chatRoutes.get('/access/files', requireAuth, checkFilesAccessHandler);

export default chatRoutes;
