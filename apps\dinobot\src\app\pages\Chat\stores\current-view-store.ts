import { create } from 'zustand'

export const VIEWS = {
    SPLIT_SCREEN: 'splitScreen',
    CHAT: 'chat',
    EXERCISE: 'exercise'
} as const

export type CurrentView = (typeof VIEWS)[keyof typeof VIEWS]

interface CurrentViewState {
    currentView: CurrentView
    setCurrentView: (view: CurrentView) => void
}

export const useCurrentViewStore = create<CurrentViewState>(set => ({
    currentView: VIEWS.SPLIT_SCREEN,
    setCurrentView: view => set({ currentView: view })
}))
