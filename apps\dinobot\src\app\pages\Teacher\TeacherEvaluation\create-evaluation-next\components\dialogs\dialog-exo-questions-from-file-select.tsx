import React from 'react'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDes<PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle
} from '@dinobot/components-ui'
import QuestionContent, {
    QuestionCardType
} from './components/question-content'
import { JsonObject, JsonArray } from '@prisma/client/runtime/library'

type DialogExoQuestionsFromFileSelectProps = {
    exoIndex: number
    quesNum: number
    open: boolean
    onOpenChange: (open: boolean) => void
    questions: {
        content: string
        desmosCode: string | number | boolean | JsonObject | JsonArray | null
        solution: string
        description: string
    }[]
}

const DialogExoQuestionsFromFileSelect = ({
    exoIndex,
    quesNum,
    onOpenChange,
    open,
    questions
}: DialogExoQuestionsFromFileSelectProps) => {
    const transformQuestions = (
        questions: {
            content: string
            desmosCode:
                | string
                | number
                | boolean
                | JsonObject
                | JsonArray
                | null
            solution: string
            description: string
        }[]
    ): QuestionCardType[] => {
        return questions.map((q, id) => ({
            knowledge: q.description || '',
            question: q.content || '',
            solution: q.solution || '',
            id,
            competences: [''],
            questionNbr: 0
        }))
    }

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-[1000px] h-[600px] 2xl:h-[800px] 2xl:max-w-[1400px]">
                <DialogHeader>
                    <DialogTitle></DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                <div className="flex">
                    <QuestionContent
                        isLoading={false}
                        exoIndex={exoIndex}
                        quesNum={quesNum}
                        onClose={() => onOpenChange(false)}
                        questions={transformQuestions(questions)}
                    />
                </div>
                <DialogFooter></DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default DialogExoQuestionsFromFileSelect
