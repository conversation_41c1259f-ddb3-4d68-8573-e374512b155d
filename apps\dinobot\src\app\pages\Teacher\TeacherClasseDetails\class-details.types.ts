export interface ClassWithRelations {
  id: string
  name: string
  classColor?: string
  scholarYear: string
  levelId: string
  createdAt: Date
  updatedAt: Date
  mainTeacher?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  students: StudentPartial[]
  themes?: Theme[]
  controls?: ControlPartial[]
}

export interface StudentPartial {
  id: string
  firstName: string
  lastName: string
  email?: string
  average?: number
  progress?: number
  behaviour?: string
}

export interface Theme {
  id: string
  name: string
  description?: string
  classId: string
}

export interface ControlPartial {
  id: string
  name: string
  description?: string
  dueDate?: Date
  type: 'homework' | 'exam' | 'quiz'
  classId: string
}

export interface ClassDetailsFormData {
  name: string
  classColor: string
  scholarYear: string
  description?: string
}

export interface StatusUpdate {
  id: string
  text: string
  color: 'green' | 'yellow' | 'red' | 'blue' | 'complete'
  title?: string
  createdAt: Date
  authorId: string
}

export interface TabProps {
  classId: string
}

export interface AppreciationParams {
  classId: string
  domainId?: string
  maxChar?: number
  tone?: number
  type: 'DOMAIN_SPECIFIC' | 'GENERAL'
}