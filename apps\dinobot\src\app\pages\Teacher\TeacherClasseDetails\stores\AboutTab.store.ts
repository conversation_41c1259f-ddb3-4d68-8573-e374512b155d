// src/pages/teacher/my-classes/stores/AboutTab.store.ts
import { create } from 'zustand'
import { ApropoStoreState, ApropoStoreActions, ClassWithRelations, Theme, ControlPartial } from '../about-tab.types'

interface ApropoStore extends ApropoStoreState, ApropoStoreActions {
  // Add theme-specific controls storage
  themeControls: Record<string, ControlPartial[]>
  setThemeControls: (themeId: string, controls: ControlPartial[]) => void
  selectedThemeId: string | null
  setSelectedThemeId: (themeId: string | null) => void
}

const useApropoStore = create<ApropoStore>((set, get) => ({
  // State
  classes: null,
  themes: [],
  controls: [],
  checkedEvaluations: [],
  themeControls: {},
  selectedThemeId: null,

  // Actions
  setClass: (classes: ClassWithRelations) => set({ classes }),
  setThemes: (themes: Theme[]) => set({ themes }),
  setControls: (controls: ControlPartial[]) => set({ controls }),
  setCheckedEvaluations: (checkedEvaluations: string[]) => set({ checkedEvaluations }),
  setThemeControls: (themeId: string, controls: ControlPartial[]) => 
    set((state) => ({
      themeControls: {
        ...state.themeControls,
        [themeId]: controls
      }
    })),
  setSelectedThemeId: (themeId: string | null) => set({ selectedThemeId: themeId })
}))

export { useApropoStore }