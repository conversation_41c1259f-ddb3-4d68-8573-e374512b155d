// src/pages/teacher/my-classes/components/EvaluationElementPreview.tsx
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Eye } from 'lucide-react'
import { Skeleton } from '@dinobot/components-ui'
import { useParams } from 'react-router-dom'
import { useAboutTab } from '../../hooks/useAboutTab'
import DialogPreviewEval from './DialogPreviewEval'

interface EvaluationElementPreviewProps {
  id: string | undefined
  loading: boolean
}

const EvaluationElementPreview: React.FC<EvaluationElementPreviewProps> = ({
  id,
  loading
}) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  const { classId } = useParams<{ classId: string }>()
  const { useControlWithMediaQuery } = useAboutTab(classId!)
  
  const { data: previewEval, isLoading } = useControlWithMediaQuery(id)

  if (isLoading || loading) {
    return (
      <div className="flex items-center gap-2">
        <Skeleton className="h-4 w-4 rounded-md" />
        <Skeleton className="h-5 w-24 rounded-md" />
      </div>
    )
  }

  return (
    <DialogPreviewEval
      exos={previewEval?.exercises ?? []}
      domain={previewEval?.assignedClass?.domain}
      level={previewEval?.assignedClass?.level}
      name={previewEval?.name}
      font={previewEval?.fontName ?? 'Roboto'}
      fontSize={previewEval?.fontSize ?? 12}
    >
      <div className="p-0 flex gap-2 justify-start hover:bg-accent hover:text-accent-foreground h-9 items-center whitespace-nowrap rounded-md text-sm font-medium cursor-pointer">
        <Eye />
        {t('eval_card.preview', { ns: 'teacher/my-class/apropo' })}
      </div>
    </DialogPreviewEval>
  )
}

export default EvaluationElementPreview