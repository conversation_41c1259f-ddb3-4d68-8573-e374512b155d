// src/pages/teacher/my-classes/components/CodeDialog.tsx
import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@dinobot/components-ui'
import { Button } from '@dinobot/components-ui'
import { Check, Copy } from 'lucide-react'
import { useParams } from 'react-router-dom'
import { useAboutTab } from '../../hooks/useAboutTab'

interface CodeDialogProps {
  trigger: React.ReactNode
}

export const CodeDialog: React.FC<CodeDialogProps> = ({ trigger }) => {
  const { t } = useTranslation(['teacher/my-class/apropo'])
  const { classId } = useParams<{ classId: string }>()
  const [isCopied, setIsCopied] = useState(false)
  const [open, setOpen] = useState(false)
  const [classCode, setClassCode] = useState<string | null>(null)

  const { useClassCodeQuery } = useAboutTab(classId!)
  const classCodeQuery = useClassCodeQuery()

  useEffect(() => {
    if (classCodeQuery.data) {
      setClassCode(classCodeQuery.data.code)
    }
  }, [classCodeQuery.data])

  const onCopy = (text: string) => {
    setIsCopied(true)
    navigator.clipboard.writeText(text)
    setTimeout(() => setIsCopied(false), 1000)
  }

  const handleTriggerClick = () => {
    if (!classCode) {
      console.error('Code not found')
      return
    }
    setOpen(true)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger
        className="flex justify-between font-semibold items-center bg-dinoBotWhite text-dinoBotBlue rounded-lg hover:bg-dinoBotWhite hover:text-dinoBotBlue gap-2 h-9 px-4 py-2"
        onClick={handleTriggerClick}
      >
        {trigger}
      </DialogTrigger>

      <DialogContent className="h-2/5">
        <DialogHeader>
          <DialogTitle className="flex justify-center p-2 text-dinoBotDarkGray">
            {t('qrcode.title')}
          </DialogTitle>
          <DialogDescription className="flex justify-center text-dinoBotGray">
            {t('qrcode.description')}
          </DialogDescription>
        </DialogHeader>

        {classCode ? (
          <div className="flex justify-center items-center font-extrabold gap-2">
            <p className="font-medium text-xl">{classCode}</p>
            <Button
              onClick={() => onCopy(classCode)}
              variant="ghost"
              className="p-0 flex justify-center items-center size-8"
            >
              {isCopied ? (
                <Check className="text-dinoBotCyan" />
              ) : (
                <Copy className="cursor-pointer text-dinoBotSky" />
              )}
            </Button>
          </div>
        ) : (
          <div className="flex justify-center items-center font-extrabold">
            {classCodeQuery.isLoading 
              ? t('qrcode.loading')
              : t('qrcode.notFound')
            }
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}