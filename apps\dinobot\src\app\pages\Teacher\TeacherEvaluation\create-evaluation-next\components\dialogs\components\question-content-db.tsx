import React from 'react'
import { useEvaluationParamsStore } from '../../../../store/evaluation-params.store'
import { useSearchSimilarQuestions } from '../../../../hooks/useSearchSimilarQuestions'
import QuestionContent from './question-content'
import { useMyClassesStore } from '../../../../../TeacherClasses/stores/MyClasses.store'


type QuestionContentDBProps = {
    exoIndex: number
    quesNum: number
    onClose: () => void
}

const QuestionContentDB = ({
    exoIndex,
    quesNum,
    onClose
}: QuestionContentDBProps) => {
    const { part, domain, level, chapter, exos } = useEvaluationParamsStore()
    const { classes } = useMyClassesStore()

    const { data, isLoading } = useSearchSimilarQuestions({
        partId: part?.id ?? '',
        partName: part?.name,
        chapterName: chapter?.title,
        levelName: level?.name, //level?.name,
        domainName: domain?.name, //domain?.name,
        exerciseType: exos[exoIndex].questionsType
    })
    return (
        <QuestionContent
            isLoading={isLoading}
            questions={data}
            exoIndex={exoIndex}
            onClose={onClose}
            quesNum={quesNum}
        />
    )
}

export default QuestionContentDB
