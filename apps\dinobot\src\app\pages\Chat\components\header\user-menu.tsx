//Hooks
import React from 'react'

//Components
import { Account } from '@dinobot/components-ui'

//---------TODO----------
import { type Session } from '@dinobot/utils'

export interface UserMenuProps {
    user: Session['user']
}

function getUserInitials(name: string) {
    const [firstName, lastName] = name.split(' ')
    return lastName ? `${firstName[0]}${lastName[0]}` : firstName.slice(0, 2)
}

export function UserMenu({ user }: UserMenuProps) {
    return (
        <div className="flex items-center justify-between" title="Mon compte">
            <Account initials={getUserInitials(user.email)} />

            {/*       <DropdownMenu>
        <DropdownMenuTrigger asChild>

        </DropdownMenuTrigger>
        <DropdownMenuContent sideOffset={8} align="start" className="w-fit">
          <DropdownMenuItem className="flex-col items-start">
            
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <form
            action={async () => {
              'use server'
              await signOut()
            }}
          >
            <button className=" relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none transition-colors hover:bg-dinoBotBlue hover:text-white focus:bg-dinoBotBlue focus:text-white data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
              Se déconnecter
            </button>
          </form>
        </DropdownMenuContent>
      </DropdownMenu> */}
        </div>
    )
}
