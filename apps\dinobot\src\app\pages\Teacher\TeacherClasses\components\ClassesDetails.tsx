import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@dinobot/components-ui';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@dinobot/components-ui';
import { Table, TableBody, TableCell, TableRow } from '@dinobot/components-ui';
import { cn } from '@dinobot/utils';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Archive, Info } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useMyClassesStore } from '../stores/MyClasses.store';
import { useMyClasses } from '../hooks/useMyClasses';
import { Class } from '../my-classes.types';
import { IconEdit } from '@dinobot/components-ui';
import AddClass from './AddClass';
import EditClass from './EditClass';

const ClassesDetails: React.FC = () => {
  const { t } = useTranslation(['teacher/my-class/classes']);
  const [columnVisibility] = useState({ id: false });
  const [open, setOpen] = useState(false);

  // Store Zustand
  const classes = useMyClassesStore((state) => state.classes);
  const archiveClassInStore = useMyClassesStore((state) => state.archiveClass);

  // Hooks TanStack Query
  const { archiveClass, isArchivingClass } = useMyClasses();

  // Fonction pour archiver une classe
  const handleArchiveClass = async (classId: string) => {
    console.log('archived: ', classId);
    try {
      // Appel API via TanStack Query
      await archiveClass(classId);

      // Mise à jour locale du store Zustand
      archiveClassInStore(classId);

      console.log('Classe archivée avec succès');
    } catch (error) {
      console.error("Erreur lors de l'archivage de la classe:", error);
    }
  };

  const columns: ColumnDef<Class>[] = [
    {
      accessorKey: 'id',
    },
    {
      accessorFn: (classe) => classe.classColor,
      id: 'color',
      cell: ({ row }) => {
        const color: string = row.getValue('color');
        return (
          <Info
            className={cn('rounded-full size-4')}
            style={{ backgroundColor: color, color: color }}
          />
        );
      },
    },
    {
      accessorFn: (classe) => classe.name,
      id: 'name',
      cell: ({ row }) => {
        const value: string = row.getValue('name');
        return <span className="font-medium">{value}</span>;
      },
    },
    {
      accessorFn: (classe) => classe.students.length,
      id: 'students',
      cell: ({ row }) => {
        const value: number = row.getValue('students');
        return (
          <span>
            {value} {t('eleves')}
          </span>
        );
      },
    },
    {
      accessorFn: (classe) => classe.scholarYear,
      id: 'annes',
      cell: ({ row }) => {
        const value: string = row.getValue('annes');
        return <span>{value}</span>;
      },
    },
    {
      accessorFn: (classe) => classe,
      id: 'actions',
      cell: ({ row }) => {
        const value: Class = row.getValue('actions');
        return (
          <div className="flex gap-2">
            <EditClass
              btnTitle={<IconEdit className="size-6" />}
              classes={value}
            />
            <Button
              variant="link"
              className="text-dinoBotGray p-0"
              onClick={() => handleArchiveClass(value.id)}
              disabled={isArchivingClass}
            >
              <Archive
                className={cn('size-4', isArchivingClass && 'animate-pulse')}
              />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: classes,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility,
    },
  });

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="link" className="text-dinoBotBlue underline">
          {t('details.title_btn')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            <div className="flex justify-between items-center">
              <span>{t('my_classes')}</span>
              <AddClass />
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="max-h-96 overflow-y-auto">
          <Table>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="py-3">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    {t('no_classes')}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        <DialogFooter className="w-full sm:justify-between">
          <Button
            variant="link"
            className="text-dinoBotRed underline text-transparent"
            type="reset"
            onClick={() => setOpen(false)}
          >
            {t('cancel')}
          </Button>
          <Link
            className="text-dinoBotGray underline"
            to="/my-classes/archive"
          >
            {t('archive')}
          </Link>
          <Button
            variant="link"
            className="text-dinoBotBlue underline"
            type="submit"
            onClick={() => setOpen(false)}
          >
            {t('save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClassesDetails;
