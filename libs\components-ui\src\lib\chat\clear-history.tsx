'use client'

import * as React from 'react'
import { toast } from 'sonner'

import { ServerActionResult } from '@dinobot/utils'
import { Button,
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
    IconSpinner
} from '..'
import { useTranslation } from 'react-i18next'

interface ClearHistoryProps {
    isEnabled: boolean
    // clearChats: () => ServerActionResult<void>
}

export function ClearHistory({
    isEnabled = false,
    // clearChats
}: ClearHistoryProps) {
    const [open, setOpen] = React.useState(false)
    const [isPending, startTransition] = React.useTransition()
    const { t } = useTranslation(['app/headers'], { keyPrefix: 'history' })

    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogTrigger asChild>
                <Button variant="ghost" disabled={!isEnabled || isPending}>
                    {isPending && <IconSpinner className="mr-2" />}
                    {t('del-history')}
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{t('confirm')}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {t('alert')}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={isPending}>
                        {t('annuler')}
                    </AlertDialogCancel>
                    <AlertDialogAction
                        disabled={isPending}
                        onClick={event => {
                            event.preventDefault()
                            startTransition(async () => {
                                // TODO - Actions: to fetched data
                                // const result = await clearChats()
                                // if (result && 'error' in result) {
                                //     toast.error(result.error)
                                //     return
                                // }

                                setOpen(false)
                            })
                        }}
                    >
                        {isPending && (
                            <IconSpinner className="mr-2 animate-spin" />
                        )}
                        {t('delete')}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
