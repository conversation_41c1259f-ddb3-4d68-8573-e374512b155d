import ResetPassword from './pages/Auth/ResetPassword/ResetPassword';
import { createBrowserRouter, Outlet } from 'react-router-dom';
import LoginPage from './pages/Auth/login/Login';
import SignupPage from './pages/Auth/signup/Signup';
import TeacherProfile from './pages/Teacher/TeacherProfile/TeacherProfile';
import MyClasses from './pages/Teacher/TeacherClasses/MyClasses';
import ChatPage from './pages/Chat/ChatPage';
import ChatById from './pages/Chat/ChatByIdPage';
import ChatLayout from './pages/Chat/ChatLayout';
import MyCourses from './pages/MyCourses/MyCourses';
import Course from './pages/MyCourses/Course/Course';
import { User } from '@dinobot/prisma';
import Evaluation from './pages/Controle/Evaluation/Evaluation';
import CreateControle from './pages/Controle/create/CreateControle';
import Controle from './pages/Controle/Controle';
import Cookies from 'js-cookie';
import ExamPage from './pages/Chat/ExamPage';
import ExamByIdPage from './pages/Chat/ExamByIdPage';
import TeacherLayout from './pages/Teacher/TeacherLayout';
import ChangePassword from './pages/Teacher/TeacherProfile/ChangePassword';

const UserLoader = async () => {
  const url = import.meta.env.APP_API_URL || 'http://localhost:3001';
  const response = await fetch(`${url}/api/auth/me`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });
  const data: { user: User } = await response.json();
  Cookies.set('session', JSON.stringify(data));
  return data;
};
const FeatureFlagLoader = async () => {
  // TODO
};

export const appRoutes = createBrowserRouter([
  {
    path: '',
    children: [
      {
        path: 'login',
        element: <LoginPage />,
      },
      {
        path: 'signup',
        element: <SignupPage />,
      },
      {
        path: 'reset-password',
        element: <ResetPassword />,
      },
    ],
  },
  {
    path: '/',
    id: 'user',
    loader: UserLoader,
    children: [
      // Student Routes
      {
        path: '',
        id: 'feature-flags',
        loader: FeatureFlagLoader,
        children: [
          {
            path: '',
            element: <ChatLayout />,
            children: [
              {
                index: true,
                element: <ChatPage />,
              },
              {
                path: 'chat/:id',
                element: <ChatById />,
              },
              {
                path: 'exam',
                element: <ExamPage />,
              },
              {
                path: 'exam/:id',
                element: <ExamByIdPage />,
              },
            ],
          },
          {
            path: 'my-classes',
            element: <MyClasses />,
          },

          {
            path: 'create-controle',
            element: <CreateControle />,
          },
        ],
      },
      // Teacher Routes
      {
        path: 'teacher-profile',
        element: <TeacherLayout />,
        children: [
          {
            path: 'infos',
            element: <TeacherProfile />,
          },
          {
            path: 'security',
            element: <ChangePassword />,
          },
        ],
      },
      {
        path: 'my-courses',
        //element: <StudentLayout />,
        children: [
          {
            index: true,
            element: <MyCourses />,
          },
          {
            path: ':courseId',
            element: <Course />,
          },
        ],
      },
      {
        path: 'controle',
        element: <Controle />,
      },
      {
        path: 'controle/:evaluationId',
        element: <Evaluation />,
      },
    ],
  },

  /*  // Account Routes

import TeacherProfile from './pages/TeacherProfile/TeacherProfile';

	export const appRoutes = createBrowserRouter([
						{
							path: '/',
							element: <TeacherProfile />,
						},
						 {
							path: 'login',
							element: <LoginPage />,
						},
					 {
							path: 'signup',
							element: <SignupPage />,
						},
									 {
							path: 'reset-password',
							element: <ResetPassword />,
						},
			/*  // Account Routes
				{
					path: 'account-deletion-requested',
					element: <AccountDeletionRequested />,
				},

				// Main App Routes
				{
					element: <AppLayout />,
					children: [
						// Chat Routes
						{
							element: <ChatLayout />,
							children: [
								{
									index: true,
									element: <ChatHome />,
								},
								{
									path: 'chat/:id',
									element: <Chat />,
								},
								{
									path: 'exam',
									element: <ExamHome />,
								},
								{
									path: 'exam/chat/:id',
									element: <ExamChat />,
								},
							],
						},

						// Mode Routes
						{
							element: <ModeLayout />,
							children: [
								{
									path: 'controle',
									element: <Controle />,
								},
								{
									path: 'controle/:evaluationId',
									element: <ControleEvaluation />,
								},
								{
									path: 'create-controle',
									element: <CreateControle />,
								},
								{
									path: 'exercise',
									element: <Exercise />,
								},
								{
									path: 'train',
									element: <Train />,
								},
							],
						},

						// Other App Routes
						{
							path: 'cgu',
							element: <CGU />,
						},
						{
							path: 'files',
							element: <Files />,
						},
						{
							path: 'katex',
							element: <Katex />,
						},
						{
							path: 'my-courses',
							element: <MyCoursesLayout />,
							children: [
								{
									index: true,
									element: <MyCourses />,
								},
								{
									path: ':courseId',
									element: <CourseDetails />,
								},
							],
						},
					],
				},
				*/
  // Auth Routes
  // {
  //   element: <AuthLayout />,
  //   children: [

  // {
  //   path: 'reset-password',
  //   element: <ResetPassword />,
  // },
  // {
  //   path: 'reset-password/finish/:key',
  //   element: <ResetPasswordFinish />,
  // },
  // {
  //   path: 'signup',
  //   element: <Signup />,
  // },
  // {
  //   path: 'signup/last-steps/:key',
  //   element: <SignupLastSteps />,
  // },
  //   ],
  // },

  // Teacher Routes
  /*{
					element: <TeacherLayout />,
					children: [
						// Profile Routes
						{
							path: 'change_password',
							element: <ChangePassword />,
						},
						{
							path: 'profil',
							element: <Profile />,
						},

						// Work Routes
						{
							path: 'my-classes',
							element: <MyClassesLayout />,
							children: [
								{
									index: true,
									element: <MyClasses />,
								},
								{
									path: 'archive',
									element: <Archive />,
								},
								{
									path: ':classId',
									element: <ClassDetails />,
								},
								{
									path: ':classId/create-evaluation',
									element: <CreateEvaluation />,
								},
								{
									path: ':classId/create-evaluation-next',
									element: <CreateEvaluationNext />,
									children: [
										{
											path: ':title',
											element: <CreateEvaluationTitle />,
										},
									],
								},
								{
									path: ':classId/schedule-assessment',
									element: <ScheduleAssessment />,
								},
							],
						},
					],
				},*/
]);
