// src/pages/teacher/my-classes/components/ThemeBlock.tsx
import React from 'react'
import { useTranslation } from 'react-i18next'
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@dinobot/components-ui'
import { Loader2, Trash } from 'lucide-react'
import { Theme, ControlPartial } from '../../about-tab.types'
import EvaluationCard from './EvaluationCard'

interface ThemeBlockProps {
  theme: Theme & { _count: { controls: number } }
  isLoading: boolean
  controls?: ControlPartial[]
}

const ThemeBlock: React.FC<ThemeBlockProps> = ({ theme, isLoading, controls }) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  
  const evaluations = controls || theme.controls || []

  const removeTheme = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    event.stopPropagation()

    try {
      if (theme._count.controls === 0) {
        // Add your delete theme logic here
        console.log('Delete theme:', theme.id)
      } else {
        console.error('Cannot delete theme with evaluations')
      }
    } catch (error) {
      console.error('Error removing theme:', error)
    }
  }

  return (
    <AccordionItem
      value={theme.id}
      className="border-2 rounded-xl border-dinoBotGray/50 overflow-hidden"
    >
      <AccordionTrigger
        className="rounded-lg px-4 bg-dinoBotLightGray text-dinoBotDarkGray flex justify-between"
      >
        <div className="flex gap-2 items-center justify-between w-full z-50">
          <span className="text-dinoBotDarkGray font-semibold">{theme.name}</span>
          {theme._count.controls === 0 && (
            <button
              className="p-2 flex gap-2 justify-start items-center shadow-none mx-2 hover:bg-gray-100 rounded"
              onClick={removeTheme}
            >
              <Trash className="text-dinoBotRed" />
            </button>
          )}
        </div>
      </AccordionTrigger>
      
      <AccordionContent className="px-4 py-2 flex gap-2 flex-wrap w-full">
        {isLoading ? (
          <Loader2 className="mr-2 size-4 animate-spin" />
        ) : (
          evaluations.map((control, i) => (
            <EvaluationCard key={i} evaluation={control} />
          ))
        )}
        {!evaluations.length && !isLoading && (
          <div className="text-dinoBotGray">
            {t('block', { ns: 'teacher/my-class/apropo' })}
          </div>
        )}
      </AccordionContent>
    </AccordionItem>
  )
}

export default ThemeBlock