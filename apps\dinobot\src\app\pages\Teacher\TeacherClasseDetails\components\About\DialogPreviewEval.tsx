// src/pages/teacher/my-classes/components/DialogPreviewEval.tsx
import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@dinobot/components-ui'
import { Button } from '@dinobot/components-ui'

interface Exercise {
  id: string
  title: string
  content?: string
  questions?: any[]
}

interface Domain {
  id: number
  name: string
}

interface Level {
  id: number
  name: string
}

interface DialogPreviewEvalProps {
  children: React.ReactNode
  exos: Exercise[]
  domain?: Domain
  level?: Level
  name?: string
  font?: string
  fontSize?: number
}

const DialogPreviewEval: React.FC<DialogPreviewEvalProps> = ({
  children,
  exos,
  domain,
  level,
  name,
  font = 'Roboto',
  fontSize = 12
}) => {
  const { t } = useTranslation([
    'teacher/my-class/classes', 
    'teacher/my-class/apropo'
  ])
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-[80vw] max-h-[80vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-center">
            {t('preview_title', { ns: 'teacher/my-class/apropo' })} - {name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="p-4">
          {/* Evaluation Header */}
          <div className="mb-6 text-center">
            <h1 className="text-2xl font-bold mb-2" style={{ fontFamily: font, fontSize: `${fontSize + 4}px` }}>
              {name}
            </h1>
            {domain && level && (
              <p className="text-gray-600" style={{ fontFamily: font, fontSize: `${fontSize}px` }}>
                {domain.name} - {level.name}
              </p>
            )}
          </div>

          {/* Exercises */}
          <div className="space-y-6">
            {exos.map((exercise, index) => (
              <div key={exercise.id} className="border-b pb-4">
                <h2 className="text-lg font-semibold mb-3" style={{ fontFamily: font, fontSize: `${fontSize + 2}px` }}>
                  {t('exercise', { ns: 'teacher/my-class/apropo' })} {index + 1}: {exercise.title}
                </h2>
                
                {exercise.content && (
                  <div 
                    className="mb-3"
                    style={{ fontFamily: font, fontSize: `${fontSize}px` }}
                    dangerouslySetInnerHTML={{ __html: exercise.content }}
                  />
                )}

                {exercise.questions && exercise.questions.length > 0 && (
                  <div className="ml-4">
                    {exercise.questions.map((question, qIndex) => (
                      <div key={qIndex} className="mb-2">
                        <p style={{ fontFamily: font, fontSize: `${fontSize}px` }}>
                          {qIndex + 1}. {question.content || question.text}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {exos.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              {t('no_exercises', { ns: 'teacher/my-class/apropo' })}
            </div>
          )}
        </div>

        <div className="flex justify-center p-4 border-t">
          <Button onClick={() => setOpen(false)} variant="outline">
            {t('close', { ns: 'teacher/my-class/apropo' })}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DialogPreviewEval