import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>b<PERSON><PERSON>,
    <PERSON><PERSON>crumbList,
    BreadcrumbSeparator
} from '@dinobot/components-ui'
import { Link } from 'react-router-dom'
import { useControlWithMedia } from '../../hooks/useControlWithMedia'
import { useClass } from '../../hooks/useClass'
import { useTranslation } from 'react-i18next'
import { useParams, useSearchParams } from 'react-router-dom'
import React from 'react'

const TitlePage = () => {
    const { t } = useTranslation('teacher/myClass/evaluation/next/titles')
    const params = useParams<{ classId: string }>()
    const [searchParams] = useSearchParams()

    const evalId = searchParams.get('evalId')

    const { data: dataClass } = useClass(params.classId)
    const { data: controle } = useControlWithMedia(evalId || undefined)

    if (!evalId) return <span>{t('create')}</span>
    return (
        <Breadcrumb className="text-dinoBotGray/90">
            <BreadcrumbList className="sm:gap-1">
                <BreadcrumbItem className="text-2xl font-bold ml-0">
                    <Link to="/my-classes">{t('class')}</Link>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem className="text-2xl font-bold">
                    <Link to={`/my-classes/${dataClass?.id}`}>
                        {dataClass?.name}
                    </Link>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem className="text-2xl font-bold">
                    {controle?.name}
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem className="text-2xl font-bold">
                    {t('update')}
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    )
}

export default TitlePage
