{"name": "@dinobot/dinobot-backend", "version": "0.0.1", "private": true, "description": "Dinobot educational platform backend API", "main": "src/app.ts", "scripts": {"dev": "tsx watch --tsconfig ./tsconfig.app.json src/app.ts", "build": "nx build", "start": "node dist/app.js", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:auth": "vitest run tests/features/auth"}, "keywords": ["hono", "education", "ai", "backend"], "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.16", "@ai-sdk/xai": "^1.2.16", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@dinobot/prisma": "workspace:*", "@dinobot/utils": "workspace:*", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/sdk-node": "^0.203.0", "@react-email/components": "^0.3.0", "@react-email/render": "^1.1.3", "ai": "^4.3.9", "axios": "^1.8.4", "langfuse": "^3.38.4", "langfuse-vercel": "^3.37.2", "nanoid": "^5.1.5", "nodemailer": "^7.0.5", "openai": "^4.95.0", "pino": "^9.6.0", "pino-caller": "^4.0.0", "pino-pretty": "^13.0.0", "react": "^19.1.0", "react-email": "^4.1.1", "rtl-detect": "^1.1.2", "stripe": "^18.0.0"}, "devDependencies": {"@hono/node-server": "^1.15.0", "@testcontainers/postgresql": "^10.24.2", "@types/bcryptjs": "^3.0.0", "@types/node": "^24.0.12", "@types/nodemailer": "^6.4.17", "@types/react": "^19.1.8", "@types/supertest": "^6.0.3", "@vitest/ui": "^3.2.4", "bcryptjs": "^3.0.2", "c8": "^10.1.3", "dotenv": "^17.2.0", "hono": "^4.8.4", "nodemon": "^3.1.10", "supertest": "^7.1.3", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4", "zod": "^3.25.56"}, "nx": {"targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"platform": "node", "outputPath": "apps/dinobot-backend/dist", "format": ["cjs"], "bundle": true, "main": "apps/dinobot-backend/src/app.ts", "tsConfig": "apps/dinobot-backend/tsconfig.app.json", "assets": ["apps/dinobot-backend/src/assets"], "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}, "alias": {"@/*": "./src/*"}}}, "configurations": {"development": {}, "production": {"esbuildOptions": {"sourcemap": false, "outExtension": {".js": ".js"}, "alias": {"@/*": "./src/*"}}}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@dinobot/dinobot-backend:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@dinobot/dinobot-backend:build:development"}, "production": {"buildTarget": "@dinobot/dinobot-backend:build:production"}}}}}}