import {
    Html,
    Head,
    Body,
    Container,
    Section,
    Text,
    Button,
    Img,
    Preview,
    Row,
    Column,
    Link
} from '@react-email/components'
import {
    AccountDeletionParams,
    BRAND_COLORS,
    SOCIAL_LINKS,
    LEGAL_LINKS
} from '../types'
import { formatEmailDate, getLangDir } from '../utils'
import { getBaseUrl, getBackendBaseUrl } from '../config'

/**
 * Account deletion confirmation email template
 * Based on main NextJS app's account-deletion-email.tsx
 */
export const AccountDeletionTemplate = async (
    params: AccountDeletionParams
) => {
    const { firstName, scheduledDeletionAt, locale = 'en' } = params
    const baseUrl = getBaseUrl()
    const backendBaseUrl = getBackendBaseUrl()
    const langDir = getLangDir(locale)
    const deletionDate = formatEmailDate(scheduledDeletionAt, locale)

    // Translation messages (simplified for now - will be enhanced with proper i18n)
    const messages = {
        en: {
            preview: 'Your DinoBot account deletion is scheduled',
            greeting: `Hello ${firstName}!`,
            instruction: `Your account deletion request has been processed. Your account will be permanently deleted on ${deletionDate}.`,
            reactivate:
                'If you change your mind, you can reactivate your account before the deletion date by logging in.',
            button: 'Reactivate Account',
            warning:
                'After the deletion date, all your data will be permanently removed and cannot be recovered.',
            contact:
                'If you have any questions, please contact our support team.',
            thanks: 'Thank you for using DinoBot.',
            signature: 'The DinoBot Team'
        },
        fr: {
            preview: 'La suppression de votre compte DinoBot est programmée',
            greeting: `Bonjour ${firstName}!`,
            instruction: `Votre demande de suppression de compte a été traitée. Votre compte sera définitivement supprimé le ${deletionDate}.`,
            reactivate:
                "Si vous changez d'avis, vous pouvez réactiver votre compte avant la date de suppression en vous connectant.",
            button: 'Réactiver le compte',
            warning:
                'Après la date de suppression, toutes vos données seront définitivement supprimées et ne pourront pas être récupérées.',
            contact:
                'Si vous avez des questions, veuillez contacter notre équipe de support.',
            thanks: "Merci d'avoir utilisé DinoBot.",
            signature: "L'équipe DinoBot"
        }
    }

    const t = messages[locale as keyof typeof messages] || messages.en

    return (
        <Html dir={langDir}>
            <Head />
            <Preview>{t.preview}</Preview>
            <Body
                style={{
                    backgroundColor: BRAND_COLORS.background,
                    fontFamily:
                        '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif'
                }}
            >
                <Container
                    style={{
                        margin: '0 auto',
                        padding: '20px 0 48px',
                        maxWidth: '560px'
                    }}
                >
                    {/* Header with Logo */}
                    <Section style={{ padding: '0 48px' }}>
                        <Img
                            src={`${backendBaseUrl}/dinobot-logo-small.svg`}
                            width="60"
                            height="60"
                            alt="DinoBot"
                            style={{
                                margin: '0 auto',
                                display: 'block'
                            }}
                        />
                    </Section>

                    {/* Main Content */}
                    <Section
                        style={{
                            padding: '24px 48px',
                            backgroundColor: '#ffffff',
                            borderRadius: '8px',
                            border: '1px solid #e5e7eb',
                            margin: '24px 0'
                        }}
                    >
                        <Text
                            style={{
                                fontSize: '24px',
                                lineHeight: '1.3',
                                fontWeight: '600',
                                color: BRAND_COLORS.text,
                                textAlign: 'center'
                            }}
                        >
                            {t.greeting}
                        </Text>

                        <Text
                            style={{
                                fontSize: '16px',
                                lineHeight: '1.5',
                                color: BRAND_COLORS.textSecondary,
                                textAlign: 'center',
                                margin: '16px 0 32px'
                            }}
                        >
                            {t.instruction}
                        </Text>

                        <Section
                            style={{
                                backgroundColor: '#FEF3C7',
                                borderRadius: '6px',
                                padding: '16px',
                                margin: '24px 0',
                                border: '1px solid #F59E0B'
                            }}
                        >
                            <Text
                                style={{
                                    fontSize: '14px',
                                    lineHeight: '1.5',
                                    color: '#92400E',
                                    textAlign: 'center',
                                    margin: '0'
                                }}
                            >
                                ⚠️ {t.warning}
                            </Text>
                        </Section>

                        <Text
                            style={{
                                fontSize: '16px',
                                lineHeight: '1.5',
                                color: BRAND_COLORS.textSecondary,
                                textAlign: 'center',
                                margin: '24px 0'
                            }}
                        >
                            {t.reactivate}
                        </Text>

                        <Section style={{ textAlign: 'center' }}>
                            <Button
                                href={`${baseUrl}/login`}
                                style={{
                                    backgroundColor: BRAND_COLORS.primary,
                                    borderRadius: '6px',
                                    color: '#ffffff',
                                    fontSize: '16px',
                                    fontWeight: '600',
                                    textDecoration: 'none',
                                    textAlign: 'center',
                                    display: 'inline-block',
                                    padding: '12px 24px',
                                    lineHeight: '1.25'
                                }}
                            >
                                {t.button}
                            </Button>
                        </Section>

                        <Text
                            style={{
                                fontSize: '14px',
                                lineHeight: '1.5',
                                color: BRAND_COLORS.textSecondary,
                                textAlign: 'center',
                                margin: '32px 0 0'
                            }}
                        >
                            {t.contact}
                        </Text>
                    </Section>

                    {/* Footer */}
                    <Section style={{ padding: '0 48px' }}>
                        <Text
                            style={{
                                fontSize: '14px',
                                lineHeight: '1.5',
                                color: BRAND_COLORS.textSecondary,
                                textAlign: 'center',
                                margin: '16px 0'
                            }}
                        >
                            {t.thanks}
                            <br />
                            {t.signature}
                        </Text>

                        {/* Social Links */}
                        <Row style={{ textAlign: 'center', margin: '16px 0' }}>
                            <Column>
                                <Link
                                    href={SOCIAL_LINKS.instagram}
                                    style={{
                                        margin: '0 8px',
                                        textDecoration: 'none'
                                    }}
                                >
                                    <Img
                                        src={`${backendBaseUrl}/instagram.png`}
                                        width="24"
                                        height="24"
                                        alt="Instagram"
                                    />
                                </Link>
                                <Link
                                    href={SOCIAL_LINKS.linkedin}
                                    style={{
                                        margin: '0 8px',
                                        textDecoration: 'none'
                                    }}
                                >
                                    <Img
                                        src={`${backendBaseUrl}/linkedin.png`}
                                        width="24"
                                        height="24"
                                        alt="LinkedIn"
                                    />
                                </Link>
                                <Link
                                    href={SOCIAL_LINKS.facebook}
                                    style={{
                                        margin: '0 8px',
                                        textDecoration: 'none'
                                    }}
                                >
                                    <Img
                                        src={`${backendBaseUrl}/facebook.png`}
                                        width="24"
                                        height="24"
                                        alt="Facebook"
                                    />
                                </Link>
                                <Link
                                    href={SOCIAL_LINKS.youtube}
                                    style={{
                                        margin: '0 8px',
                                        textDecoration: 'none'
                                    }}
                                >
                                    <Img
                                        src={`${backendBaseUrl}/youtube.png`}
                                        width="24"
                                        height="24"
                                        alt="YouTube"
                                    />
                                </Link>
                            </Column>
                        </Row>

                        {/* Legal Links */}
                        <Text
                            style={{
                                fontSize: '12px',
                                lineHeight: '1.5',
                                color: BRAND_COLORS.textSecondary,
                                textAlign: 'center',
                                margin: '16px 0'
                            }}
                        >
                            <Link
                                href={LEGAL_LINKS.privacy}
                                style={{
                                    color: BRAND_COLORS.textSecondary,
                                    margin: '0 8px'
                                }}
                            >
                                Privacy Policy
                            </Link>
                            |
                            <Link
                                href={LEGAL_LINKS.terms}
                                style={{
                                    color: BRAND_COLORS.textSecondary,
                                    margin: '0 8px'
                                }}
                            >
                                Terms of Service
                            </Link>
                            |
                            <Link
                                href={LEGAL_LINKS.contact}
                                style={{
                                    color: BRAND_COLORS.textSecondary,
                                    margin: '0 8px'
                                }}
                            >
                                Contact Us
                            </Link>
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    )
}

export default AccountDeletionTemplate
