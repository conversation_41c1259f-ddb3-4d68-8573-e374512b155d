// src/pages/teacher/my-classes/components/EvaluationAlert.tsx
import React from 'react'
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@dinobot/components-ui'
import { DialogState } from '../../about-tab.types'

interface EvaluationAlertProps {
  title?: string
  description?: string
  cancelLabel?: string
  evaluationAlertOpen: boolean
  dispatch: (action: Partial<DialogState>) => void
}

export const EvaluationAlert: React.FC<EvaluationAlertProps> = ({
  title = 'Es-tu sûr ?',
  description = 'Cette action va supprimer cet élément définitivement de nos serveurs.',
  cancelLabel = 'Annuler',
  evaluationAlertOpen,
  dispatch
}) => {
  return (
    <AlertDialog open={evaluationAlertOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={() => dispatch({ evaluationAlertOpen: false })}
          >
            {cancelLabel}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export default EvaluationAlert