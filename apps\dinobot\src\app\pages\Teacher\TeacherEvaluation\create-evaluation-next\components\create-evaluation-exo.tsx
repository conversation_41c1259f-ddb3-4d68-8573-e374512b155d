import { <PERSON>roll<PERSON><PERSON> } from '@dinobot/components-ui'
import React, { useEffect, useState, useTransition } from 'react'
import ExoContent from './exo-content'
import { Eye, Loader2, Plus } from 'lucide-react'
import { Button } from '@dinobot/components-ui'
import DialogParamsEvaluation from './dialogs/dialog-params-evaluation'
import { useNavigate } from 'react-router-dom'
import { useEvaluationParamsStore } from '../../store/evaluation-params.store'
import DialogPreviewEval from '../../components/dialogs/dialog-preview-eval'
import { selectUseScheduleAssessmentStore } from '../../store/use-schedule-assessment-store'
import { useParams, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import ExoContentLoading from './exo-content-loading'
import { ControlWithPartialRelations, Level } from '@dinobot/prisma'
import { useControlWithMedia } from '../../hooks/useControlWithMedia'
import { useDomainsByLevel } from '../../hooks/useDomainsByLevel'
import { useCreateControl } from '../../hooks/useCreateControl'
import { useUpdateControl } from '../../hooks/useUpdateControl'
import { isEmptyOrNull } from '@dinobot/utils'

const CreateEvaluationExo = () => {
    const { t } = useTranslation('teacher/myClass/evaluation/next')
    const navigate = useNavigate()
    const [searchParams] = useSearchParams()
    const evalId = searchParams.get('evalId')
    const params = useParams<{ classId: string }>()
    const {
        isOnLoadExo,
        addExo,
        havePossibilityToAdd,
        exos,
        domain,
        setLevelAndDomain,
        level,
        evalProps,
        setExos,
        setEvalProps,
        isUpdate,
        setUpdating,
        reset
    } = useEvaluationParamsStore()
    const setSelectedControl =
        selectUseScheduleAssessmentStore.use.setSelectedControl()
    const [isAdd, onAdd] = useTransition()
    const [loadId, setLoad] = useState<1 | 2 | 3>()

    // Fetch control with media if evalId exists
    const { data: controlData, error: controlError, isLoading } = useControlWithMedia(evalId || undefined)
    
    // Fetch domains by level for the control
    const { data: domainsData } = useDomainsByLevel(controlData?.assignedClass?.levelId)
    
    // Mutations for create and update
    const createControlMutation = useCreateControl()
    const updateControlMutation = useUpdateControl()
    useEffect(() => {
        if (isOnLoadExo && !evalId) navigate('create-evaluation')
        return () => {
            reset()
        }
    }, [isOnLoadExo, evalId, navigate, reset])

    // Handle control data loading when it's available
    useEffect(() => {
        if (controlData && domainsData && evalId) {
            setUpdating(true)
            const { exercises, ...rest } = controlData
            
            setLevelAndDomain(
                rest.assignedClass?.level as Level,
                domainsData.find(domain => domain.name === rest.domainName)!
            )
            setExos(exercises!)
            setEvalProps(rest as ControlWithPartialRelations)
        }
    }, [controlData, domainsData, evalId, setLevelAndDomain, setExos, setEvalProps, setUpdating])

    // Handle control error
    useEffect(() => {
        if (controlError) {
            console.error('Error loading control:', controlError)
        }
    }, [controlError])
    const saveExo = async (status: 'DRAFT' | 'ASSIGNED' | 'IN_PROGRESS') => {
        const { assignedClass, theme, submissions, ...rest } = evalProps || {}
        const data = { ...rest, exercises: exos, status }
        
        if (isUpdate && data.id) {
            return await updateControlMutation.mutateAsync({
                id: data.id,
                data,
                level: level!,
                domain: domain!
            })
        }
        
        return await createControlMutation.mutateAsync({
            data,
            level: level!,
            domain: domain!
        })
    }
    const save = (
        status: 'DRAFT' | 'ASSIGNED' | 'IN_PROGRESS',
        loadId: 1 | 2
    ) => {
        setLoad(loadId)
        onAdd(() => {
            saveExo(status)
            reset()
            navigate(`/my-classes/${params.classId}`)
            setUpdating(false)
        })
    }
    const scheduleEvaluation = () => {
        setLoad(3)
        onAdd(async () => {
            setSelectedControl(await saveExo('IN_PROGRESS'))
            reset()
            setUpdating(false)
            navigate('schedule-assessment')
        })
    }
    return (
        <div>
            <div className="flex justify-between">
                <h2 className="font-medium text-lg">{t('title')}</h2>
                <div className="flex gap-4">
                    <DialogParamsEvaluation />
                    <DialogPreviewEval
                        exos={exos}
                        domain={domain!}
                        level={level!}
                        name={evalProps?.name}
                        font={evalProps?.fontName ?? 'Roboto'}
                        fontSize={evalProps?.fontSize ?? 12}
                    >
                        <div className=" flex flex-col items-center gap-1 h-fit p-0">
                            <span className="rounded-lg p-2 bg-dinoBotBlue text-dinoBotWhite text-wrap">
                                <Eye />
                            </span>
                            {t('preview')} <br /> {t('prv_eval')}
                        </div>
                    </DialogPreviewEval>
                </div>
            </div>
            <ScrollArea className="">
                {isLoading ? (
                    <ExoContentLoading />
                ) : (
                    exos.map((exo, i) => (
                        <ExoContent key={i} exoNum={i} exo={exo} />
                    ))
                )}
                <div className="flex justify-center mt-4 w-full">
                    <Button
                        variant="outline"
                        className={`text-dinoBotBlue hover:text-dinoBotVibrantBlue border-dinoBotBlue px-2 ${exos.at(-1)?.questions?.at(-1)?.content ? '' : 'hidden'}`}
                        onClick={() => addExo({ questions: [{}] })}
                        disabled={!havePossibilityToAdd()}
                    >
                        <Plus />
                        {t('add_exo')}
                    </Button>
                </div>
            </ScrollArea>
            <div className="mt-6 flex justify-evenly px-[18%]">
                {/* <Button variant='outline' className='border-dinoBotGray' onClick={()=>save('DRAFT',1)}disabled={isEmptyOrNull(exos.at(0)?.questions?.at(0)?.content)||(isAdd&&loadId===1)}>
                    {(isAdd&&loadId===1) ? <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        {t('submit.loading')}
                    </>:
                    isUpdate?t('submit.update_br'):t('submit.save_br')}</Button> */}
                <Button
                    variant="default"
                    className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue text-dinoBotWhite"
                    onClick={() => save('IN_PROGRESS', 2)}
                    disabled={
                        isEmptyOrNull(exos.at(0)?.questions?.at(0)?.content) ||
                        createControlMutation.isPending ||
                        updateControlMutation.isPending ||
                        (isAdd && loadId === 2)
                    }
                >
                    {isAdd && loadId === 2 ? (
                        <>
                            <Loader2 className="mr-2 size-4 animate-spin" />
                            {t('submit.loading')}
                        </>
                    ) : isUpdate ? (
                        t('submit.update')
                    ) : (
                        t('submit.create')
                    )}
                </Button>
                {evalProps?.status !== 'ASSIGNED' && (
                    <Button
                        variant="default"
                        className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue text-dinoBotWhite"
                        onClick={scheduleEvaluation}
                        disabled={
                            isEmptyOrNull(
                                exos.at(0)?.questions?.at(0)?.content
                            ) ||
                            createControlMutation.isPending ||
                            updateControlMutation.isPending ||
                            (isAdd && loadId === 3)
                        }
                    >
                        {isAdd && loadId === 3 ? (
                            <>
                                <Loader2 className="mr-2 size-4 animate-spin" />
                                {t('submit.loading')}
                            </>
                        ) : (
                            t('submit.shedule')
                        )}
                    </Button>
                )}
            </div>
        </div>
    )
}

export default CreateEvaluationExo
