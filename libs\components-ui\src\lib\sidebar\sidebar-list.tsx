//API
//TODO---------
// import { clearChats, getChats } from '@/app/[locale]/actions'

//Components
import { SidebarItems } from './sidebar-items'

//Hooks
import React, { cache } from 'react'
import { useTranslation } from 'react-i18next'
import { ClearHistory } from '../chat/clear-history'

//Translations
// import { getTranslations } from 'next-intl/server'

interface SidebarListProps {
    userId?: string
    children?: React.ReactNode
}

export const loadChats = cache( (userId?: string) => {
    // return await getChats(userId)
    return []
})

export function SidebarList({ userId }: SidebarListProps) {
    const {t} = useTranslation(['app.headers'], { keyPrefix: 'history' })
    const chats = loadChats(userId)

    return (
        <div className="flex flex-1 flex-col overflow-hidden">
            <div className="flex-1 overflow-overlay">
                {chats?.length ? (
                    <div className="space-y-2 px-2">
                        <SidebarItems chats={chats} userId={userId} />
                    </div>
                ) : (
                    <div className="p-8 text-center">
                        <p className="text-sm text-muted-foreground">
                            {t('no-his')}
                        </p>
                    </div>
                )}
            </div>
            <div className=" w-full flex items-center justify-center p-4 mb-3">
                {/* <ThemeToggle /> */}
                <ClearHistory
                    // clearChats={clearChats}
                    isEnabled={chats?.length > 0}
                />
            </div>
        </div>
    )
}
