import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    Di<PERSON>Header,
    DialogTitle,
    DialogTrigger
} from '@dinobot/components-ui'
import { IconInfo } from '@dinobot/components-ui'
import React, { useState } from 'react'
import ChapterListPart from './components/chapter-list-part'
import { ScrollArea } from '@dinobot/components-ui'
import QuestionContentDB from './components/question-content-db'
import { useTranslation } from 'react-i18next'

type DialogExoQuestionsFromDBProps = {
    exoIndex: number
    quesNum: number
}

const DialogExoQuestionsFromDB = ({
    exoIndex,
    quesNum
}: DialogExoQuestionsFromDBProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/db')
    const [open, setOpen] = useState(false)
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger className="flex flex-col items-center justify-center hover:no-underline gap-1 h-fit">
                <div className="rounded-full flex justify-center items-center border p-1 border-dinoBotVibrantBlue/30 bg-dinoBotWhite/80">
                    <img
                        src={'/dinobot-tv-sm.svg'}
                        width={34}
                        height={34}
                        alt="from db icon"
                    />
                </div>
                <span className="text-sm font-medium">{t('btn_title')}</span>
            </DialogTrigger>
            <DialogContent className="max-w-[1200px] h-[600px] 2xl:h-[800px] 2xl:max-w-[1600px]">
                <DialogHeader>
                    <DialogTitle className="flex gap-2 text-lg items-center">
                        {t('title')} <IconInfo fill="#5c5c5c" />
                    </DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                <div className="flex w-full">
                    <ScrollArea className="w-80 h-[500px] 2xl:h-[700px]">
                        <ChapterListPart />
                    </ScrollArea>
                    <QuestionContentDB
                        exoIndex={exoIndex}
                        quesNum={quesNum}
                        onClose={() => setOpen(false)}
                    />
                </div>
                <DialogFooter></DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default DialogExoQuestionsFromDB
