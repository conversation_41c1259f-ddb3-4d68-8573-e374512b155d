{"completeInscription": {"student": {"1": {"preview": "You're just one step away from accessing Dinobot! 🎓", "subject": "You're just one step away from accessing Dinobot! 🎓", "greeting": "Hi ,", "intro": "You started your registration on Dinobot yesterday, but you just have one last step to complete.", "buttonText": "Complete my registration →", "mainTitle": "Reminder: with <PERSON><PERSON>, you'll have access to:", "features": ["A personalized chatbot to help you with your subjects", "Exercises adapted to your level", "An exam mode to prepare effectively"], "bottomText": "It will only take you 2 minutes!", "signature": "See you soon on <PERSON><PERSON>,", "team": "The Dinobot Team"}, "2": {"preview": "How Léa improved by 3 points thanks to <PERSON><PERSON> 📈", "subject": "How Léa improved by 3 points thanks to <PERSON><PERSON> 📈", "greeting": "Hi ,", "intro": "I wanted to share <PERSON><PERSON><PERSON>'s story, a final year student:\n\n\"Before Dinobot, I was struggling with math. Now, I can ask all my questions to the chatbot, redo exercises until I understand, and I feel really ready for the exam. My average went from 11 to 14!\"\n\nWhat if it's your turn to improve? You can still complete your registration:", "buttonText": "I'm joining Dinobot now →", "mainTitle": "What awaits you:", "features": ["A virtual teacher available 24/7 to answer your questions", "Exercises that adapt to your difficulties", "All exam papers with detailed corrections"], "bottomText": "Registration takes 2 minutes and you can start right away.", "signature": "Good luck,", "team": "The Dinobot Team", "ps": "P.S.: You can also test our avatar mode where the chatbot really talks to you! 🎥"}, "3": {"preview": "Last chance: we help you complete your registration", "subject": "Last chance: we help you complete your registration", "greeting": "Hi ,", "intro": "It's been a week since you started your registration on Dinobot, and we wouldn't want you to miss out!\n\nWe know that sometimes registering on a new platform can seem complicated.\n\nThat's why we offer you:", "buttonText": "Complete quickly →", "mainTitle": "Why is it worth it?", "features": ["Imagine being able to ask any question about your courses and get a clear answer immediately", "No more blocks on your homework", "No more stress before tests"], "bottomText": "Or write to us if you have questions: <EMAIL>", "signature": "We hope to see you soon on Dinobot!", "team": "The Dinobot Team", "unsubscribe": true, "extraButton": "I'm starting now →"}}, "teacher": {"1": {"preview": "Complete your Dinobot registration in 2 minutes", "subject": "Complete your Dinobot registration in 2 minutes", "greeting": "Hello ,", "intro": "You started your registration on Dinobot yesterday. You just have one step left to access the platform and start creating your classes.", "buttonText": "Complete my registration →", "mainTitle": "Once registered, you'll be able to:", "features": ["Create and manage your classes", "Generate personalized assessments", "Track your students' progress"], "bottomText": "", "signature": "Best regards,", "team": "The Dinobot Team", "secondButton": "Access my platform →"}, "2": {"preview": "\"My students are more autonomous\" - Teacher Testimonial", "subject": "\"My students are more autonomous\" - Teacher Testimonial", "greeting": "Hello ,", "intro": "<PERSON>, a mathematics teacher at Victor Hugo High School, shared her feedback:\n\n\"Since I've been using Dinobot, my students come to class better prepared. They can practice at their own pace, and I can focus on the essentials in class. The automatically generated assessments save me precious time!\"\n\nYou too can transform your teaching method:", "buttonText": "Complete my registration →", "mainTitle": "The advantages for your classes:", "features": ["Individualized tracking of each student", "Automatic generation of exercises and assessments", "Assisted correction to save time", "Class progress dashboard"], "bottomText": "", "signature": "Best regards,", "team": "The Dinobot Team", "secondButton": "Discover the platform →"}, "3": {"preview": "Need help with your Dinobot registration?", "subject": "Need help with your Dinobot registration?", "greeting": "Hello ,", "intro": "Your registration on Dinobot has been pending for a week.\n\nAre you encountering any particular difficulty?\n\nWe understand that adopting a new educational tool represents a time investment. That's why we offer you:", "buttonText": "Complete my registration →", "mainTitle": "Or schedule a 15-minute call with our educational team to:", "features": ["Con<PERSON>tely discover the platform", "See how to integrate it into your courses", "Answer all your questions"], "bottomText": "Alternative: Write to <NAME_EMAIL>\n\nWe are here to support you in your students' success.", "signature": "Best regards,", "team": "The Dinobot Team", "secondButton": "Book my slot →", "unsubscribe": true}}}, "common": {"followUs": "Follow us!", "company": "OuiActive", "address": "Head office: 166 <PERSON><PERSON>. <PERSON>, 69003 Lyon", "legalNotice": "Legal notice", "dataProtection": "Data protection", "automaticEmail": "This is an automatic email, please do not reply", "unsubscribeText": "Stop receiving these emails:", "unsubscribeLink": "unsubscribe"}, "requestExtensionResponse": {"greeting": "Hello {prenom} {nom},", "detailsTitle": "Request details:", "subjectsLabel": "Requested subjects/levels:", "dateLabel": "Request date:", "statusLabel": "Status:", "buttonText": "Access my teacher dashboard →", "finalText": "To view your request history, go to your teacher dashboard > My requests.", "signature": "Best regards,", "team": "The Dinobot Team", "ACCEPTED": {"preview": "Your extension request has been approved - Dinobot", "subject": "✅ Your extension request has been approved - Dinobot", "mainTitle": "We are pleased to inform you that your request for extension of subjects and levels on the Dinobot platform has been approved.", "statusLabel": "Approved", "commentLabel": "Administrator comment:", "bottomText": "Your new access rights are now active. You can now:", "benefits": ["Create and manage your classes for these new subjects/levels", "Generate adapted assessments", "Access corresponding educational resources"]}, "REJECTED": {"preview": "Your extension request has been rejected - Dinobot", "subject": "❌ Your extension request has been rejected - Dinobot", "mainTitle": "We inform you that your request for extension of subjects and levels on the Dinobot platform has been rejected.", "statusLabel": "Rejected", "commentLabel": "Reason for rejection:", "bottomText": "If you would like more information about this decision or submit a new request with additional elements, please do not hesitate to contact us."}}}