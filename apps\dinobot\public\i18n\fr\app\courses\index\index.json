{"title": "Mes Classes", "dialog": {"title": "Code Classe", "trigger": "+ Ajouter une classe", "description": "Rentrez le code classe à 10 caractères donné par votre professeur·e ou l’établissement.", "cancel": "Annuler", "submit": "Valider"}, "success": {"title": "Classe ajoutée", "text": "La classe a bien été ajoutée."}, "error": {"length": "Le code doit contenir 10 caractères.", "invalid": "Le code est invalide.", "required": "Le code est requis."}, "tabs": {"about": "A propos", "calendar": "<PERSON><PERSON><PERSON>", "scoring": "Notation"}, "card": {"title": "Professeur·e principal·e :", "evaluation": "Evaluation", "form": "Fiche d'exercice", "draft": "Brouillon"}, "accordion": {"not-found": "Aucune evaluation trouvée."}}