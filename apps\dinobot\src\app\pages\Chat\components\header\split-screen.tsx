'use client'

import { Columns2, FileText, MessageCircle } from 'lucide-react'
import { cn } from '@dinobot/utils'
import { useLocation } from 'react-router-dom'
import { useCurrentViewStore, VIEWS } from '../../stores/current-view-store'

export default function SplitScreen() {
    const path = useLocation().pathname
    const { currentView, setCurrentView } = useCurrentViewStore()

    return (
        path.includes('train') && (
            <div className="flex flex-row justify-center items-center gap-2 bg-dinoBotLightGray/40 rounded-lg">
                <div
                    className={cn(
                        'flex justify-center p-1 rounded cursor-pointer',
                        currentView === VIEWS.EXERCISE &&
                            'bg-white border-2 border-dinoBotGray/30 rounded-l-lg'
                    )}
                    onClick={() => setCurrentView(VIEWS.EXERCISE)}
                >
                    <FileText
                        className={cn(
                            'hidden md:block text-gray-500 w-5 h-5',
                            currentView === VIEWS.EXERCISE && 'text-black/80'
                        )}
                    />
                </div>
                <div
                    className={cn(
                        'flex justify-center p-1 rounded cursor-pointer',
                        currentView === VIEWS.SPLIT_SCREEN &&
                            'bg-white border-2 border-dinoBotGray/30'
                    )}
                    onClick={() => setCurrentView(VIEWS.SPLIT_SCREEN)}
                >
                    <Columns2
                        className={cn(
                            'hidden md:block text-gray-500 w-5 h-5',
                            currentView === VIEWS.SPLIT_SCREEN &&
                                'text-black/80'
                        )}
                    />
                </div>
                <div
                    className={cn(
                        'flex justify-center p-1 rounded cursor-pointer',
                        currentView === VIEWS.CHAT &&
                            'bg-white border-2 border-dinoBotGray/30 rounded-r-lg'
                    )}
                    onClick={() => setCurrentView(VIEWS.CHAT)}
                >
                    <MessageCircle
                        className={cn(
                            'hidden md:block text-gray-500 w-5 h-5',
                            currentView === VIEWS.CHAT && 'text-black/80'
                        )}
                    />
                </div>
            </div>
        )
    )
}
