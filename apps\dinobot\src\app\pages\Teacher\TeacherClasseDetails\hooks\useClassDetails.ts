import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

export const useClassDetails = (classId: string) => {
  const apiClient = useAuthApiClient()

  return useQuery({
    queryKey: ['class', classId],
    queryFn: async () => {
      const response = await apiClient.get(`/api/classes/${classId}`)
      return response.data
    },
    enabled: !!classId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export const useClassStudents = (classId: string) => {
  const apiClient = useAuthApiClient()

  return useQuery({
    queryKey: ['class-students', classId],
    queryFn: async () => {
      const response = await apiClient.get(`/api/classes/${classId}/students`)
      return response.data
    },
    enabled: !!classId,
  })
}

export const useUpdateClass = () => {
  const apiClient = useAuthApiClient()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: { classId: string; updates: any }) => {
      const response = await apiClient.put(`/api/classes/${data.classId}`, data.updates)
      return response.data
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['class', variables.classId] })
      queryClient.invalidateQueries({ queryKey: ['classes'] })
    },
  })
}

export const useCreateStatusUpdate = () => {
  const apiClient = useAuthApiClient()
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: { 
      classId: string
      text: string
      color: 'green' | 'yellow' | 'red' | 'blue' | 'complete'
      title?: string
    }) => {
      const response = await apiClient.post(`/api/classes/${data.classId}/status`, data)
      return response.data
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['class', variables.classId] })
    },
  })
}