import { logger } from '@/lib/logger'
import prisma from '@/common/database/prisma'
import { PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'
import { NotationSubmissionType } from './types'

export async function createPlannedEvaluation(
    plannedEvaluation: PlannedEvaluationPartialWithRelations
) {
    const result = await prisma.control.update({
        where: {
            id: plannedEvaluation.controlId!
        },
        data: {
            status: plannedEvaluation.control?.status,
            plannedEvaluations: {
                create: {
                    classId: plannedEvaluation.classId!,
                    availableDate: plannedEvaluation.availableDate!,
                    dueDate: plannedEvaluation.dueDate!,
                    description: plannedEvaluation.description!,
                    title: plannedEvaluation.title!,
                    retries: plannedEvaluation.retries!,
                    timeLimit: plannedEvaluation.timeLimit!
                }
            }
        },
        include: {
            plannedEvaluations: true
        }
    })
    const createdPlannedEvaluation =
        result.plannedEvaluations[result.plannedEvaluations.length - 1]

    return createdPlannedEvaluation
}

export async function fetchPlannedEvaluations(classId: string) {
    return prisma.plannedEvaluation.findMany({
        where: {
            classId
        },
        include: {
            control: true
        },
        distinct: ['controlId']
    })
}

export async function fetchPlannedEvaluationsAndControls(classId: string) {
    return prisma.plannedEvaluation.findMany({
        where: {
            classId
        },
        include: {
            control: {
                include: {
                    submissions: {
                        include: {
                            _count: true
                        }
                    },
                    exercises: {
                        include: {
                            questions: true
                        }
                    }
                }
            },
            class: {
                include: {
                    students: {
                        include: {
                            _count: true
                        }
                    }
                }
            }
        }
    })
}

export async function deletePlannedEvaluation(plannedEvaluationId: string) {
    return prisma.plannedEvaluation.delete({
        where: {
            id: plannedEvaluationId
        }
    })
}

// Alias pour la fonction avec le typo dans le nom original
export async function deletePlannedEvlauation(id: string) {
    return deletePlannedEvaluation(id)
}

export const fetchStudentSubmissionByPlannedEvaluationId = async (
    plannedEvaluationId: string
) => {
    return prisma.studentSubmission.findMany({
        where: {
            control: {
                plannedEvaluations: {
                    some: {
                        id: plannedEvaluationId
                    }
                }
            }
        },
        include: {
            answers: {
                include: {
                    controlQuestion: true
                }
            },
            control: {
                include: {
                    submissions: {
                        include: {
                            _count: true
                        }
                    },
                    exercises: {
                        include: {
                            questions: true
                        }
                    }
                }
            },
            student: true
        }
    })
}

export const fetchStudentsByPlannedEvaluationId = async (
    plannedEvaluationId: string
) => {
    return prisma.student.findMany({
        where: {
            submissions: {
                some: {
                    control: {
                        plannedEvaluations: {
                            some: {
                                id: plannedEvaluationId
                            }
                        }
                    }
                }
            }
        },
        include: {
            user: true
        }
    })
}

export const fetchStudentsByClassId = async (classId: string) => {
    return prisma.student.findMany({
        where: {
            classId: classId
        }
    })
}

export const submitNotationToDB = async (notation: NotationSubmissionType) => {
    if (notation.id) {
        return prisma.studentSubmission.update({
            where: {
                id: notation.id
            },
            data: {
                answers: {
                    updateMany: notation.answers?.map(a => ({
                        where: {
                            id: a.id
                        },
                        data: {
                            feedback: a.feedback,
                            score: a.score
                        }
                    }))
                },
                globalFeedback: notation.globalFeedback,
                globalScore: notation.globalScore
            }
        })
    } else {
        return prisma.studentSubmission.create({
            data: {
                student: {
                    connect: { id: notation.studentId! }
                },
                control: {
                    connect: { id: notation.controlId! }
                },
                submissionState: notation.submissionState,
                globalFeedback: notation.globalFeedback,
                globalScore: notation.globalScore,
                correctedAt: new Date()
            }
        })
    }
}

export const giveBackCopy = async (plannedEvaluationId: string) => {
    return await prisma.plannedEvaluation.update({
        where: {
            id: plannedEvaluationId
        },
        data: {
            isCorrected: true,
            submitCorrectedAt: new Date()
        }
    })
}

export const fetchNotationByClassIdAndStudentId = async (
    classId: string,
    studentId: string,
    order?: 'asc'
) => {
    const data = await prisma.plannedEvaluation.findMany({
        where: {
            isCorrected: true,
            control: {
                assignedClassId: classId
            }
        },
        orderBy: {
            submitCorrectedAt: order
        },
        include: {
            studentSubmission: {
                where: {
                    student: {
                        userId: studentId
                    }
                }
            },
            control: true
        }
    })
    return data
}

export const fetchNotationMinMaxBycontrole = async (controlId: string) => {
    const scores = await prisma.studentSubmission.aggregate({
        where: {
            plannedEvaluation: {
                controlId: controlId
            }
        },
        _min: {
            globalScore: true
        },
        _max: {
            globalScore: true
        }
    })
    return scores
}
