import React, { Suspense } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ArrowLeft } from 'lucide-react'
import { cn } from '@dinobot/utils'
import ClassDetailsForm from './components/ClassDetailsForm'
import { useClassDetailsStore } from './stores/ClassDetails.store'

const ClassDetails: React.FC = () => {
  const { t, i18n } = useTranslation(['teacher-class-details'])
  const { classId } = useParams<{ classId: string }>()
  const navigate = useNavigate()
  const searchParams = useSearchParams()
  const dir = i18n.dir()
  const { currentClass } = useClassDetailsStore()

  const handleBackClick = () => {
    navigate('/my-classes')
  }

  const flagAppreciations = process.env.REACT_APP_APPRECIATIONS === 'true' || false

  return (
    <div className="size-full overflow-hidden flex flex-col py-7 gap-8">
      <div className="w-full flex flex-col gap-4 px-24">
        <div className="flex gap-3 items-center">
          <button
            onClick={handleBackClick}
            className="size-12 flex justify-center items-center border-2 border-dinoBotLightGray rounded-md hover:bg-dinoBotBlue/20"
          >
            <ArrowLeft className={cn(dir === 'rtl' && 'rotate-180')} />
          </button>
          <div className="text-4xl font-bold text-dinoBotGray/90">
            {currentClass?.name}
          </div>
        </div>
      </div>
      <div className="w-full grow overflow-y-auto px-10 2xl:px-32">
        <div className="size-full">
          <Suspense fallback={<div>Loading...</div>}>
            <ClassDetailsForm 
              classId={classId!} 
              flagAppreciations={flagAppreciations} 
            />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

export default ClassDetails