import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@dinobot/components-ui';
import { Table, TableBody, TableCell, TableRow } from '@dinobot/components-ui';
import { cn } from '@dinobot/utils';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Info, Trash2, ChevronLeft } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { useMyClassesStore } from '../stores/MyClasses.store';
import { useMyClasses } from '../hooks/useMyClasses';
import { Class } from '../my-classes.types';
import SelectAnnes from './SelectAnnes';

// Générer une plage d'années (équivalent à getRangeYears)
const generateYearRange = (years: number): string[] => {
  const currentYear = new Date().getFullYear();
  const range: string[] = [];
  
  for (let i = -25; i <= years - 25; i++) {
    const startYear = currentYear + i;
    const endYear = startYear + 1;
    range.push(`${startYear}-${endYear}`);
  }
  
  return range;
};

const ClassesArchived: React.FC = () => {
  const { t } = useTranslation('teacher/my-classes-archive');
  const navigate = useNavigate();
  const [columnVisibility] = useState({ id: false });
  
  // Store Zustand
  const classesArchive = useMyClassesStore((state) => state.classesArchive);
  const setClassesArchive = useMyClassesStore((state) => state.setClassesArchive);
  const deleteClassInStore = useMyClassesStore((state) => state.deleteClass);
  
  // Hooks TanStack Query
  const { useArchivedClassesQuery, deleteClass, isDeletingClass } = useMyClasses();
  
  // État local
  const years = generateYearRange(50);
  const [classYear, setClassYear] = useState<string>(years[25]); // Année actuelle au milieu

  // ✅ Utilisation du hook pour récupérer les classes archivées
  const {
    data: archivedClassesData,
    isLoading,
    error,
    refetch
  } = useArchivedClassesQuery(classYear);

  // Mettre à jour le store quand les données changent
  useEffect(() => {
    if (archivedClassesData) {
      setClassesArchive(archivedClassesData);
    }
  }, [archivedClassesData, setClassesArchive]);

  // Handler pour changer l'année
  const handleYearChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setClassYear(event.target.value);
  };

  // Fonction pour supprimer définitivement une classe
  const handleDeleteClass = async (classId: string) => {
    try {
      // Appel API via TanStack Query
      await deleteClass(classId);
      
      // Mise à jour locale du store Zustand
      deleteClassInStore(classId);
      
      console.log('Classe supprimée définitivement');
    } catch (error) {
      console.error('Erreur lors de la suppression de la classe:', error);
    }
  };

  // Définition des colonnes
  const columns: ColumnDef<Class>[] = [
    {
      accessorKey: 'id',
    },
    {
      accessorFn: (classe) => classe.classColor,
      id: 'color',
      cell: ({ row }) => {
        const color: string = row.getValue('color');
        return (
          <Info
            className={cn('rounded-full size-4')}
            style={{ backgroundColor: color, color: color }}
          />
        );
      },
    },
    {
      accessorFn: (classe) => classe.name,
      id: 'name',
      cell: ({ row }) => {
        const value: string = row.getValue('name');
        return <span className="font-medium">{value}</span>;
      },
    },
    {
      accessorFn: (classe) => classe.students.length,
      id: 'students',
      cell: ({ row }) => {
        const value: number = row.getValue('students');
        return (
          <span>
            {value} {t('eleves')}
          </span>
        );
      },
    },
    {
      accessorFn: (classe) => classe.scholarYear,
      id: 'annes',
      cell: ({ row }) => {
        const value: string = row.getValue('annes');
        return <span>{value}</span>;
      },
    },
    {
      accessorFn: (classe) => classe,
      id: 'bulletin',
      cell: ({ row }) => {
        const value: Class = row.getValue('bulletin');
        return (
          <Link
            to={`/bulletin/${value.id}?note=true`}
            className="text-dinoBotGray underline hover:text-dinoBotBlue"
          >
            {t('bulletin')}
          </Link>
        );
      },
    },
    {
      accessorFn: (classe) => classe.id,
      id: 'actions',
      cell: ({ row }) => {
        const classId: string = row.getValue('actions');
        return (
          <Button
            variant="link"
            className="text-dinoBotRed border px-2 border-dinoBotRed hover:bg-dinoBotRed hover:text-white"
            onClick={() => handleDeleteClass(classId)}
            disabled={isDeletingClass}
          >
            <Trash2 className={cn('size-4', isDeletingClass && 'animate-pulse')} />
          </Button>
        );
      },
    },
  ];

  // Configuration de la table
  const table = useReactTable({
    data: classesArchive,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility,
    },
  });

  // États de chargement et d'erreur
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-full text-red-500">
        {t('error_loading_archived_classes')}
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center w-full gap-2 relative bg-white min-h-screen">
      {/* Bouton retour */}
      <Button
        variant="ghost"
        className="absolute top-4 left-6 rounded-full bg-dinoBotBlue p-1.5 scale-75 text-white hover:bg-dinoBotVibrantBlue"
        onClick={() => navigate('/my-classes')}
      >
        <ChevronLeft className="size-5" />
      </Button>

      {/* Titre */}
      <div className="mt-16 text-lg font-bold text-dinoBotDarkGray">
        <h2>{t('title')}</h2>
      </div>

      {/* Sélecteur d'année */}
      <div className="mb-4">
        <SelectAnnes
          value={classYear}
          onChange={handleYearChange}
        />
      </div>

      {/* Table des classes archivées */}
      <div className="overflow-y-auto max-h-[450px] w-5/6 custom-scroller">
        <Table className="overflow-y-auto custom-scroller">
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-3">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {t('no_archived_classes')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ClassesArchived;