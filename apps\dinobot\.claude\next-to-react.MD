
# 🚀 **Prompt complet de migration de page - Next.js vers React avec Zustand**

## 🗒️ **Exigences de migration**

Je souhaite migrer **\[PAGE\_NAME]** de Next.js vers React en respectant notre architecture et nos conventions établies.

## 🏢 **Architecture requise :**

```
src/pages/[category]/[page-name]/
├── [PageName].tsx              ← Composant principal de la page
├── [page-name].types.ts        ← Interfaces & types TypeScript
├── components/
│   └── [PageName]Form.tsx      ← Composant principal du formulaire / contenu
├── hooks/
│   └── use[PageName].ts        ← Hooks TanStack Query
└── stores/
    └── [PageName].store.ts     ← Store Zustand pour la gestion d’état local/global
```

## 🔧 **Stack technique à utiliser :**

1. **Axios Interceptor** - Utiliser nos hooks AppContext :

   * `useApiClient()` pour les routes publiques
   * `useAuthApiClient()` pour les routes protégées
2. **TanStack Query** :

   * `useMutation` pour les modifications (POST, PUT, DELETE)
   * `useQuery` pour la récupération de données (GET)
   * `useQueryClient` pour l’invalidation du cache
3. **Zustand** - Pour la gestion d’état transverse ou local à la feature :

   * Créer un store Zustand dans `stores/[PageName].store.ts`
   * Centraliser les états utiles partagés entre composants de la page
4. **react-i18next** :

   * Pattern : `useTranslation('[category]-[page]')`
   * Clés : `t('section.subsection.key')`
   * Fichiers de traduction FR/EN
5. **Remplacements de composants Next.js** :

   * `next/image` → `<img>`
   * `next/link` → `<a>` ou `Link`
   * `useRouter()` → `useNavigate()`
   * `redirect()` → `navigate()` ou `window.location.href`

## 📝 **Pattern de gestion des formulaires :**

```typescript
// État du formulaire via Zustand
import { use[PageName]Store } from '@/stores/[PageName].store'

const formData = use[PageName]Store((state) => state.formData)
const setFormData = use[PageName]Store((state) => state.setFormData)

const mutation = useMutation({
  mutationFn: async (data) => {
    const response = await apiClient.post('/api/endpoint', data)
    return response.data
  },
  onSuccess: () => {
    // Ex: reset du store, redirect, invalidation queries
  }
})

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault()
  try {
    await mutation.mutateAsync(formData)
  } catch (error) {
    console.error('Échec de l’opération :', error)
  }
}
```

## 🌐 **Configuration des traductions :**

* `public/i18n/fr/[category]-[page].json`
* `public/i18n/en/[category]-[page].json`

## 🎨 **Styling & Composants :**

* Conserver les styles et classes CSS
* Garder la structure JSX
* Maintenir le responsive, les animations, et le HTML sémantique

## ⚠️ **Exigences critiques :**

1. **PAS de localStorage pour l’auth** - Utiliser cookies HttpOnly
2. **PAS de toasts d’erreurs dans les composants**
3. **Zustand** :

   * Pour gérer les états globaux ou ceux utilisés par plusieurs composants
   * Pour stocker et synchroniser les états soumis dans les formulaires
4. **Type safety** - Interfaces TypeScript
5. **États de chargement et validation client**
6. **Cache invalidation après mutations**

## 🔄 **Pattern de gestion d’état combiné React + Zustand :**

```typescript
// Zustand store
const formData = use[PageName]Store((state) => state.formData)
const setFormData = use[PageName]Store((state) => state.setFormData)

const mutation = useMutation({
  mutationFn: async (data) => {
    const response = await apiClient.post('/api/endpoint', data)
    return response.data
  },
  onSuccess: () => {
    // redirect, invalidation
  }
})

const isLoading = mutation.isPending || query.isLoading
```

## 📱 **Responsive & Accessibilité :**

* Responsive : classes sm:, md:, lg:, xl:
* Accessibilité : aria-labels, roles, navigation clavier, HTML sémantique

## 🚨 **Pièges courants à éviter :**

* ❌ Pas de localStorage
* ❌ Pas de toasts d’erreur
* ❌ Pas de hardcoding d’API endpoints
* ❌ Ne pas oublier les states dans **Zustand si partagé**
* ❌ Pas de hooks Next.js

## ✅ **Checklist de validation :**

* [ ] Page sans erreur
* [ ] API fonctionnelle avec auth
* [ ] Formulaire valide
* [ ] Traductions FR/EN fonctionnelles
* [ ] States gérés avec **Zustand** si nécessaire
* [ ] Chargements visibles
* [ ] Responsive et accessible
* [ ] Navigation correcte
* [ ] Invalidation cache après mutation

---

## 🖨️ **CODE ORIGINAL À MIGRER :**

\[Code Next.js original : Page, actions, composants liés, types, style]

---

## 🎯 **Livrables attendus :**

1. `[PageName].tsx`
2. `components/[PageName]Form.tsx`
3. `hooks/use[PageName].ts`
4. `stores/[PageName].store.ts` **← store Zustand**
5. `[page-name].types.ts`
6. `public/i18n/fr/[category]-[page].json`
7. `public/i18n/en/[category]-[page].json`

Chaque fichier doit être complet, fonctionnel, prêt sans placeholder.
