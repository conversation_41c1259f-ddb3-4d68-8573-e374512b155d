import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useClassDetailsStore } from '../stores/ClassDetails.store'
import ListAppreciations from './ListAppreciations'
import AppreciationDomain from './AppreciationDomain'
import AppreciationDomainNext from './AppreciationDomainNext'
import AppreciationDomainResult from './AppreciationDomainResult'
import AppreciationGeneral from './AppreciationGeneral'
import AppreciationGeneralResult from './AppreciationGeneralResult'

interface AppreciationsTabProps {
  classId: string
}

enum AppreciationType {
  LIST = 0,
  DOMAIN = 1,
  DOMAIN_NEXT = 2,
  DOMAIN_RESULT = 3,
  GENERAL = 4,
  GENERAL_RESULT = 5
}

const AppreciationsTab: React.FC<AppreciationsTabProps> = ({ classId }) => {
  const { t } = useTranslation('teacher-class-details')
  const [currentView, setCurrentView] = React.useState(AppreciationType.LIST)
  const [domainStep, setDomainStep] = React.useState(1)
  const [generalStep, setGeneralStep] = React.useState(1)

  const domainViews = [
    <></>,
    <AppreciationDomain key="domain" classId={classId} onNext={() => setCurrentView(AppreciationType.DOMAIN_NEXT)} />,
    <AppreciationDomainNext key="domainNext" classId={classId} onNext={() => setCurrentView(AppreciationType.DOMAIN_RESULT)} />,
    <AppreciationDomainResult key="domainResult" classId={classId} onBack={() => setCurrentView(AppreciationType.LIST)} />,
    <></>
  ]

  const generalViews = [
    <></>,
    <AppreciationGeneral key="general" classId={classId} onNext={() => setCurrentView(AppreciationType.GENERAL_RESULT)} />,
    <AppreciationGeneralResult key="generalResult" classId={classId} onBack={() => setCurrentView(AppreciationType.LIST)} />,
    <></>
  ]

  useEffect(() => {
    setCurrentView(AppreciationType.LIST)
  }, [])

  const renderCurrentView = () => {
    switch (currentView) {
      case AppreciationType.LIST:
        return <ListAppreciations classId={classId} onCreateAppreciation={(type) => {
          if (type === 'DOMAIN') {
            setCurrentView(AppreciationType.DOMAIN)
          } else if (type === 'GENERAL') {
            setCurrentView(AppreciationType.GENERAL)
          }
        }} />
      case AppreciationType.DOMAIN:
      case AppreciationType.DOMAIN_NEXT:
      case AppreciationType.DOMAIN_RESULT:
        return domainViews[domainStep]
      case AppreciationType.GENERAL:
      case AppreciationType.GENERAL_RESULT:
        return generalViews[generalStep]
      default:
        return <ListAppreciations classId={classId} onCreateAppreciation={(type) => {
          if (type === 'DOMAIN') {
            setCurrentView(AppreciationType.DOMAIN)
          } else if (type === 'GENERAL') {
            setCurrentView(AppreciationType.GENERAL)
          }
        }} />
    }
  }

  return (
    <div className="flex flex-col gap-2 h-full">
      {currentView === AppreciationType.LIST ? (
        renderCurrentView()
      ) : (
        <>
          <Select
            onValueChange={(value) => setCurrentView(Number(value) as AppreciationType)}
            value={currentView.toString()}
            defaultValue={AppreciationType.DOMAIN.toString()}
          >
            <SelectTrigger
              className={`w-[200px] ${
                currentView === AppreciationType.DOMAIN ? 'bg-dinoBotBlue text-white' : ''
              }`}
            >
              <SelectValue placeholder={t('appreciations.select_type')} />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value={AppreciationType.DOMAIN.toString()}>
                  {t('appreciations.domain_specific')}
                </SelectItem>
                <SelectItem value={AppreciationType.GENERAL.toString()}>
                  {t('appreciations.general')}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <div className="flex-1">
            {renderCurrentView()}
          </div>
        </>
      )}
    </div>
  )
}

export default AppreciationsTab