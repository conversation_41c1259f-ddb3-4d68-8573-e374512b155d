import {useAccountStore} from '@dinobot/stores'
import { ChevronRight, CircleUserRound, Settings } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { Link, useLocation } from 'react-router-dom'
import React, { useEffect, useState } from 'react'
import { SelectIntl } from '@dinobot/components-ui'
import { User } from '@dinobot/prisma'
import DisconnectButton from './disconnect'

const ProfLinks = [
    // {
    // 	// icon: <Wrench />,
    // 	label: 'Travail',
    // 	links: [
    // 	{
    // 		name: 'Fiches exercices créées',
    // 		path: '/created-controls',
    // 		activeIf: ['/created-controls','/new-control']
    // 	},
    // 	// {
    // 	// 	name: 'Programmer une évaluation',
    // 	// 	path: '/schedule-assessment',
    // 	// 	activeIf: ['/schedule-assessment']
    // 	// },
    // 	// {
    // 	//   name: 'Contrôles attribués',
    // 	//   path: '/assigned-controls',
    // 	//   activeIf: ['/assigned-controls']
    // 	// },
    // 	// {
    // 	//   name: 'Me<PERSON> cours',
    // 	//   path: '/',
    // 	//   activeIf: ['/']
    // 	// }
    // 	]
    // },
    {
        // icon: <CircleUserRound />,
        label: 'bilan',
        links: [
            {
                name: 'my_class',
                path: '/my-classes',
                activeIf: ['/my-classes']
            }
            // {
            //   name: 'Profils d’élèves',
            //   path: '/',
            //   activeIf: ['/']
            // },
            // {
            //   name: 'Mon calendrier',
            //   path: '/',
            //   activeIf: ['/']
            // },
            // {
            //   name: 'Notes et moyennes',
            //   path: '/',
            //   activeIf: ['/']
            // },
        ]
    },
    {
        icon: <CircleUserRound />,
        label: 'my_compte',
        links: [
            {
                name: 'my_profile',
                path: '/teacher-profile/infos',
                activeIf: ['/teacher-profile/infos']
            },
            {
                name: 'password',
                path: '/teacher-profile/security',
                activeIf: ['/teacher-profile/security']
            },
            {
                name: 'logout',
                path: '/',
                activeIf: ['/']
            }
        ]
    }
    // {
    // 	// icon: <FolderOpen />,
    // 	label: 'SAV',
    // 	links: [
    // 	{
    // 		name: 'Contacter le support',
    // 		path: '/',
    // 		activeIf: ['/']
    // 	},
    // 	{
    // 		name: 'FAQ',
    // 		path: '/',
    // 		activeIf: ['/']
    // 	}
    // 	]
    // }
]
type TeacherSideBarProps = { user: User; logOut: () => void }

function TeacherSideBar({ user, logOut }: TeacherSideBarProps) {
    const { t,i18n } = useTranslation(['app/sidebar'])
    const pathname = useLocation().pathname
    const dir = i18n.dir()
    const [active, setActive] = useState(true)
    const { setUser } = useAccountStore()
    useEffect(() => {
        setUser(user)
    }, [user, setUser])
    return (
        <div
            className={`relative ${active ? 'w-80 px-10 py-2' : 'w-0'} bg-dinoBotRed h-full transition-all duration-300 ease-in-out`}
        >
            <div className=" flex flex-col justify-between h-full overflow-hidden ">
                <div>
                    <SelectIntl />
                </div>
                <div className="w-96 flex flex-col gap-8 justify-between">
                    {ProfLinks.map((item, index) => (
                        <div
                            key={index}
                            className=" text-white flex flex-col gap-2"
                        >
                            <div className="flex gap-2 text-2xl items-center font-extrabold text-nowrap">
                                <div>{item.icon}</div>
                                <p>{t(item.label)}</p>
                            </div>
                            <div className="flex flex-col text-lg font-light gap-1">
                                {item.links.map(link => {
                                    if (link.name === 'logout') {
                                        return (
                                            <DisconnectButton
                                                key={link.path}
                                                onClick={() => logOut()}
                                            />
                                        )
                                    }
                                    return (
                                        <Link
                                            className={`hover:underline transition-all duration-200 text-nowrap pl-2 ${link.activeIf.includes(pathname) ? 'font-semibold underline' : ''}`}
                                            to={link.path}
                                            key={link.path}
                                        >
                                            {t(link.name)}
                                        </Link>
                                    )
                                })}
                            </div>
                        </div>
                    ))}
                </div>
                <div>
                    <div className="size-10 rounded-full bg-dinoBotWhite text-dinoBotRed flex justify-center items-center hover:text-dinoBotRed/80 transition-all duration-300 cursor-pointer">
                        <Settings className="size-8" />
                    </div>
                </div>
            </div>

            <button
                className={`absolute ${dir === 'ltr' ? '-right-8 rounded-r-3xl' : '-left-8 rounded-l-3xl'} top-1/2 w-8 h-32 -translate-y-1/2 bg-dinoBotRed flex items-center justify-center text-white pr-1 after:bg-transparent after:absolute after:size-4 after:z-auto after:-top-4 ${dir === 'ltr' ? 'after:left-0 after:rounded-bl-3xl after:shadow-[-5px_5px_0px_1px_#e22351]' : 'after:right-0 after:rounded-br-3xl after:shadow-[5px_5px_0px_1px_#e22351]'} before:bg-transparent before:absolute before:size-4 before:z-auto before:-bottom-4 ${dir === 'ltr' ? 'before:left-0 before:rounded-tl-3xl before:shadow-[-5px_-5px_0px_1px_#e22351]' : 'before:right-0 before:rounded-tr-3xl before:shadow-[5px_-5px_0px_1px_#e22351]'}`}
                onClick={() => setActive(a => !a)}
            >
                <ChevronRight
                    className={`${active ? (dir === 'ltr' ? 'rotate-180' : '') : dir === 'ltr' ? 'rotate-0' : 'rotate-180'} transition duration-300 ease-in-out`}
                />
            </button>
        </div>
    )
}

export default TeacherSideBar
