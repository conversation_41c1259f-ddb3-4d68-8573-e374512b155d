import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
// import {
//   Select,
//   SelectContent,
//   SelectGroup,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import { Button } from '@/components/ui/button';
// import InfoTooltip from '@/components/ui/info-tooltip';
// import ErrorTooltip from '@/components/ui/error-tooltip';
// import { TimePicker } from '@/components/shared/ui/time-picker';
import { toast } from 'sonner';
import { useCreateControle } from '../hooks/useCreateControle';
import {
  useCreateControleStore,
  useCreateControleSelectors,
} from '../stores/CreateControle.store';
import { Chapter } from '../CreateControle.types';
import InfoTooltip from '@dinobot/components-ui/lib/ui/info-tooltip';
import ErrorTooltip from '@dinobot/components-ui/lib/ui/error-tooltip';
import {
  Button,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  TimePicker,
} from '@dinobot/components-ui';
import { getLangProps } from '@dinobot/utils';
// import { Chapter } from '../create-controle.types';
// import { getLangProps } from '@/lib/utils/string.utils';

interface CtrlFromDbProps {
  chapters: Chapter[];
  getParts: (chapterId: string) => void;
}

const CtrlFromDb: React.FC<CtrlFromDbProps> = ({ chapters, getParts }) => {
  const { t, i18n } = useTranslation(['app/mode']);
  const locale = i18n.language;

  const ctrlInfo = useCreateControleSelectors.use.ctrlInfo();
  const time = useCreateControleSelectors.use.time();
  const showError = useCreateControleSelectors.use.showError();

  const { updateCtrlInfo, setTime, setSelectedChapter, setShowError } =
    useCreateControleStore();

  const { submitForm, isSubmitting } = useCreateControle();
  const [selectedChapterLocal, setSelectedChapterLocal] = useState<
    string | null
  >(null);

  useEffect(() => {
    if (selectedChapterLocal) {
      updateCtrlInfo('chapterId', selectedChapterLocal);
      getParts(selectedChapterLocal);
    }
  }, [selectedChapterLocal, updateCtrlInfo, getParts]);

  useEffect(() => {
    // Reset selected chapter when chapters change (domain change)
    setSelectedChapterLocal(null);
    setSelectedChapter(null);
  }, [chapters, setSelectedChapter]);

  const handleSubmit = () => {
    const isValidTime =
      time &&
      (time.getHours() > 0 || time.getMinutes() > 0 || time.getSeconds() > 0);

    if (ctrlInfo.chapterId && isValidTime) {
      setShowError(false);
      submitForm({
        chapterId: ctrlInfo.chapterId,
        time: time,
      });
    } else {
      setShowError(true);
      toast.info(t('tinfo'));
    }
  };

  const handleChapterChange = (value: string) => {
    setSelectedChapterLocal(value);
    setSelectedChapter(value);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div>
        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
          {t('chapter.title')}{' '}
          {(ctrlInfo.chapterId.length <= 0 && !showError) ||
          ctrlInfo.chapterId.length > 0 ? (
            <InfoTooltip message="Choisir un chapitre" />
          ) : (
            <ErrorTooltip message="Vous devrez choisir un chapitre" />
          )}
        </div>

        <Select onValueChange={handleChapterChange}>
          <SelectTrigger className="w-[400px] max-w-full">
            {selectedChapterLocal ? (
              <SelectValue placeholder={t('chapter.placeholder')} />
            ) : (
              t('chapter.placeholder')
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {chapters.map((chapter) => (
                <SelectItem key={chapter.id} value={chapter.id}>
                  {getLangProps({
                    base: 'title',
                    obj: chapter,
                    lang: locale,
                  })}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[400px] flex items-center justify-between gap-1">
        <p>{t('duration')}</p>
        <TimePicker date={time} setDate={(value) => setTime(value!)} />
      </div>

      <div className="w-full flex justify-center items-center mt-6">
        <Button
          className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            t('submit')
          )}
        </Button>
      </div>
    </div>
  );
};

export default CtrlFromDb;
