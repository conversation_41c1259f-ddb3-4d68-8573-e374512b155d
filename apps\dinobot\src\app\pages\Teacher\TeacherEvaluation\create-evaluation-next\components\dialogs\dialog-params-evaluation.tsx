import { Settings } from 'lucide-react'
import React, { useEffect } from 'react'
import ParamEvaluation from '../../../create-evaluation/components/param-evaluation'
import { useTranslation } from 'react-i18next'
import { selectEvaluationParamsStore } from '../../../store/evaluation-params.store'
import { useParams } from 'react-router-dom'
import { toast } from 'sonner'
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@dinobot/components-ui'
import { useClassById } from '../../../hooks/useClassById'
import { useDomainsByLevel } from '../../../hooks/useDomainsByLevel'
import { useThemesByClassId } from '../../../hooks/useThemesByClassId'

const DialogParamsEvaluation = () => {
    const { t } = useTranslation('teacher/myClass/evaluation/dialog/params')
    const [open, setOpen] = React.useState(false)
    const setThemes = selectEvaluationParamsStore.use.setThemes()
    const setDomains = selectEvaluationParamsStore.use.setDomains()
    const params = useParams<{ classId: string }>()

    // Fetch class data
    const { data: classData, error: classError } = useClassById(params.classId)
    
    // Fetch domains by level
    const { data: domainsData, error: domainsError } = useDomainsByLevel(classData?.levelId)
    
    // Fetch themes by class ID
    const { data: themesData, error: themesError } = useThemesByClassId(params.classId)

    // Update domains in store when data changes
    useEffect(() => {
        if (domainsData) {
            setDomains(domainsData)
        }
        if (domainsError) {
            console.error('Error fetching domains:', domainsError)
        }
    }, [domainsData, domainsError, setDomains])

    // Update themes in store when data changes
    useEffect(() => {
        if (themesData) {
            setThemes(themesData)
        }
        if (themesError) {
            toast.error(t('error_theme'))
        }
    }, [themesData, themesError, setThemes, t])

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger className=" flex flex-col items-center gap-1 h-fit p-0">
                <span className="rounded-lg p-2 bg-dinoBotBlue text-dinoBotWhite text-wrap">
                    <Settings />
                </span>
                {t('param')} <br /> {t('prm_eval')}
            </DialogTrigger>
            <DialogContent className="h-[620px] max-w-[900px]">
                <DialogHeader className="flex items-center mb-4">
                    <DialogTitle>{t('title')}</DialogTitle>
                </DialogHeader>
                <ParamEvaluation isUpdate={true} setOpen={setOpen} />
                <DialogFooter></DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default DialogParamsEvaluation
