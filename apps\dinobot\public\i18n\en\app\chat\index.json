{"[id]": {"continue": "To continue chatting together, log in!", "env": {"part1": "The environment variable", "part2": "is missing!"}, "voire": "see the statement", "voirc": "see the solution"}, "profile": {"mycompte": "My account", "profil": {"title": "Profile", "profile": "My profile", "email": "Email address", "info": "Personal information", "edit-profile": "Edit profile", "nom": "Last name", "prenom": "First name", "genre": "Gender", "niveau": "Level", "delete-account-section": "Delete my account", "delete-account-description": "Deleting this account is a permanent action. The deletion will be effective after a 30-day period.", "genders": {"male": "Male", "female": "Female", "other": "Other"}, "birth-date": "Date of birth", "enter-birth-date": "Enter your date of birth", "select-birth-date": "Select your date of birth", "levels": {"Sixième": "Sixth", "Cinquième": "Fifth", "Quatrième": "Fourth", "Troisième": "Third", "Seconde": "Second", "Première": "First", "Terminale": "Final", "L1 (CGPE)": "L1 (CGPE)", "L2 (CGPE)": "L2 (CGPE)", "Autre": "Other"}, "enter-name": "Enter your last name", "entre-prenom": "Enter your first name", "select-genre": "Select a gender", "select-niveau": "Select a level", "error-update": "Profile update attempt failed", "profile-updated": "Profile updated", "update-info": "Update my information", "nodefine": "Undefined"}, "delete-account": {"locale": "en", "delete-account-button": "Delete account", "common": {"warning-title": "Account Deletion", "warning-message": "WARNING - Account Deletion", "permanent-deletion-warning": "You are about to permanently delete your Dinobot account.", "items-deleted": "The following items will be deleted:", "consequences": "Consequences:", "consequence1": "Your access to the platform will be immediately suspended", "consequence2": "Your data will be permanently deleted in 30 days", "consequence3": "You can cancel this request during these 30 days", "irreversible-action": "This action is irreversible after 30 days."}, "teacher": {"warning-title": "Account Deletion", "item1": "All your conversations with chatbots", "item2": "All your created classes", "item3": "All created evaluations", "item4": "All completed corrections", "item5": "All given appreciations"}, "student": {"warning-title": "Delete account?", "item1": "All your conversations with chatbots", "item2": "All your uploaded files"}, "cancel": "Cancel", "continue-deletion": "Continue with deletion", "confirm-deletion": "Confirm Account Deletion", "enter-password-to-confirm": "Please enter your password to confirm account deletion:", "password-placeholder": "Enter your password", "password-required": "Password is required", "immediate-suspension-warning": "This action will immediately suspend your access to Dinobot.", "processing": "Processing...", "confirm-deletion-button": "Delete my account", "incorrect-password": "Incorrect password", "error-occurred": "An error occurred. Please try again later.", "deletion-requested-title": "Account Deletion Requested", "deletion-requested-message": "You have requested to delete your Dinobot account.", "account-suspended-message": "Your account is now suspended and will be permanently deleted on {date} at {time}.", "confirmation-email-sent": "A confirmation email has been sent to {email}.", "cancellation-instructions": "You can cancel this request before this date by clicking the button below.", "cancel-deletion-button": "Cancel Deletion", "logoutButton": "Logout", "loading": "Loading...", "reactivate-account-button": "Reactivate my account"}, "change-password": {"title": "Change password", "ancien": "Old password", "ancien-placeholder": "Enter your old password", "new": "New password", "new-placeholder": "Enter your new password", "confirm": "Confirm new password", "confirm-placeholder": "Confirm your password", "update": "Update my password", "psw-incompatible": "Password and confirmation do not match", "psw-dif": "Enter a password different from the current", "no-autorized": "This operation is not authorized", "unauthorized": "Unauthorized", "eronne": "The old password is incorrect", "psw-change": "Password changed", "error": "An error occurred"}, "paiement": {"title": "Payment", "abone": "Subscription", "benef": "Enjoy access to all features and unlimited usage with the DinoBot Pro subscription!", "access": "Access payment center", "status": "Your subscription status:", "timeup": "Your trial ends on:", "error": "An error occurred", "essai": "Free trial", "actif": "Active", "annule": "Canceled", "paiement-incomplet": "Incomplete payment", "expire": "Incomplete payment (subscription expired)", "retart": "Late payment", "impaye": "Unpaid", "en-pause": "On hold", "inactif": "Inactive"}}, "account": {"cancelDeletionDialog": {"title": "Reactivate Your Account", "description": "Please enter your password to reactivate your account and cancel the scheduled deletion.", "passwordPlaceholder": "Enter your password", "passwordRequired": "Password is required to reactivate.", "cancelButton": "Cancel", "confirmButton": "Reactivate Account", "success": {"accountReactivated": "Your account has been successfully reactivated!"}, "error": {"generic": "An error occurred. Please try again."}}}, "emptyScreen": {"h1": "Welcome to DinoBot!", "p-emloyon": "<PERSON><PERSON>, your artificial intelligence partner from emlyon business school, is here to support you in your academic and professional success by offering you personalized, fast, and innovative assistance.", "p": "Your platform to master school subjects! I'm here to break down all the concepts and exercises that you find challenging.", "chat": "In Chat mode, chat with <PERSON><PERSON><PERSON> and ask all sorts of questions related to your subject!", "exam": "In Exam mode, try your hand at exams with the help of <PERSON><PERSON><PERSON>, who will guide you in the right direction to solve them!", "else": "In Training mode, try exercises with the help of <PERSON><PERSON><PERSON>, who will guide you in the right direction to solve them!", "connexion": "<PERSON><PERSON>", "is-connect": "You don't seem to be logged in, click here to fix that!", "connect-toi": "Log in to continue using DinoBot!", "send": "Send a message...", "send-m": "Send a message", "add-file": "Add an Image or PDF file", "formule": "Add a mathematical formula", "send-vocal": "Remove voice message", "speek": "Speak to <PERSON><PERSON>ot", "rm-pdf": "Remove PDF", "rm-img": "Remove image", "registration": "Recording...", "Une erreur est survenue": "An error occurred", "Tentative de modification du profil échouée": "Profile update attempt failed", "Profil modifié": "Profile updated"}, "exam": {"title": "To start, choose an exercise to tackle", "chooseExercise": "Choose an exercise", "dialog": "To start, choose an exercise to tackle", "transcript": "Audio transcript:", "p1": "Taking a quick glance at the solution won't hurt, especially if it helps you move forward and perform better in the future!", "p2": "Open the file below to view the solution to the exercise!", "slt": "Exercise solution", "sol": "The solution to this exercise has not been posted yet, check back later!", "solution": "solution", "enonce": "statement", "enonce-exo": "Exercise statement", "salut": "Hi, it's me again <PERSON><PERSON><PERSON>! In this mode, you can easily practice exam exercises, and you can count on me to ensure the correction!", "mais": "But most importantly, if you encounter any issues with the exercise, don't hesitate to ask me questions! I would be happy to help you solve it!", "open": "Open the file below to view the statement of the exercise!"}, "exam-dialog": {"bac": "Baccalaureate", "bem": "Certificate", "year": "Year", "categorie": "Category"}, "access": {"messages": {"limit": "You've reached your daily limit of free messages.", "success": "Access approved"}, "files": {"limit": "You've reached your daily limit of free files.", "success": "Access approved"}, "error": "Something went wrong"}}