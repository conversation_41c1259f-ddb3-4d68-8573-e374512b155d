import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useSearchParams } from 'react-router-dom'
import { useClassDetailsStore } from '../stores/ClassDetails.store'
import ScoringTable from './ScoringTable'
import ScoringDetails from './ScoringDetails'

interface ScoringTabProps {
  classId: string
}

const ScoringTab: React.FC<ScoringTabProps> = ({ classId }) => {
  const { t } = useTranslation('teacher-class-details')
  const [searchParams] = useSearchParams()
  const [currentView, setCurrentView] = React.useState(1)
  const [selectedScoring, setSelectedScoring] = React.useState<string | null>(null)

  const views = [
    <></>,
    <ScoringTable key="scoring" classId={classId} onSelectScoring={(scoringId) => {
      setSelectedScoring(scoringId)
      setCurrentView(2)
    }} />,
    <ScoringDetails key="scoring_details" scoringId={selectedScoring} classId={classId} />,
    <></>
  ]

  useEffect(() => {
    const scoringId = searchParams.get('scoringId')
    if (scoringId) {
      setSelectedScoring(scoringId)
      setCurrentView(2)
    } else {
      setCurrentView(1)
    }
  }, [searchParams])

  return (
    <div className="h-full">
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          {t('scoring.title')}
        </h2>
      </div>
      <div className="h-[calc(100%-4rem)]">
        {views[currentView]}
      </div>
    </div>
  )
}

export default ScoringTab