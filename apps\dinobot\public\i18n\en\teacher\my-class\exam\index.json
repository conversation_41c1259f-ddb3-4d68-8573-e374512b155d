{"list": {"name": "Exam Name", "date": "Exam Date", "copy": "Number of Exam <PERSON>pies", "empty": "No exams found."}, "add": {"btn_title": "Add an Exam", "title": "Add an Exam", "name": "Exam Name", "name_placeholder": "name...", "date": "Exam Date", "date_placeholder": "Choose a date", "exam": "Exam Statement", "exam_placeholder": "The file must be in .xlsx, .docx, or .pdf format, with a maximum size of 25 MB", "solution": "Exam <PERSON>", "solution_placeholder": "The file must be in .xlsx format, with a maximum size of 25 MB", "replace": "Replace the file", "upload": "Upload a file", "loading": "Processing", "add": "Add"}, "exam_papers": {"title": "exam:", "name": "Student Name", "date": "Copy Import Date", "status": "Status", "fail": "Failed", "correct": "Corrected", "note": "Table Note", "note_sup": "Additional Note", "note_total": "Total Note", "invalid": "Please enter a valid number", "max_invalid": "The note must be between 0 and", "error_updadte": "Error during modification", "menu": {"download": "Download Copy", "update": "Update Table Note", "update_sup": "Update Additional Note", "delete": "Delete Copy"}, "succses": "Note updated successfully", "error": "Error updating the note", "add": {"error": "An unknown error occurred", "btn_title": "Import Copies", "title": "Import Copies", "loading": "Processing", "correct": "Correct"}, "export": {"in_prog": "Export in progress...", "export": "Export Notes", "error": "Error during export"}, "drop": {"file": "Selected files:", "hint": "Drag and drop your compressed file here", "clic": "or click to select a file", "format": "Accepted formats: .zip, .rar, up to 25 MB"}}}