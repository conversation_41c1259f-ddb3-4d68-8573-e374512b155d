import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { LangfuseExporter } from 'langfuse-vercel';
import { logger } from '@/lib/logger';

// Initialize OpenTelemetry SDK with Langfuse
export function initializeTelemetry() {
  logger.info('Initializing telemetry...');
  try {
    const langfuseExporter = new LangfuseExporter({
      debug: process.env.NODE_ENV === 'development',
    });

    const sdk = new NodeSDK({
      serviceName: 'dinobot-backend',
      traceExporter: langfuseExporter,
      instrumentations: [
        getNodeAutoInstrumentations({
          // Disable fs instrumentation to reduce noise
          '@opentelemetry/instrumentation-fs': {
            enabled: false,
          },
        }),
      ],
    });

    sdk.start();

    logger.info('Telemetry initialized with Langfuse');

    // Return SDK for graceful shutdown
    return sdk;
  } catch (error) {
    logger.error('Failed to initialize telemetry:', error);
    return null;
  }
}
