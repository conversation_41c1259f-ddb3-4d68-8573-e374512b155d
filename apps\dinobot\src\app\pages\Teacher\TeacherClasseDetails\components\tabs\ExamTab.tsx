import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useSearchParams } from 'react-router-dom'
import { useClassDetailsStore } from '../stores/ClassDetails.store'
import ListExams from './ListExams'
import ListExamPapers from './ListExamPapers'

interface ExamTabProps {
  classId: string
}

const ExamTab: React.FC<ExamTabProps> = ({ classId }) => {
  const { t } = useTranslation('teacher-class-details')
  const [searchParams] = useSearchParams()
  const [currentView, setCurrentView] = React.useState(1)
  const [selectedExam, setSelectedExam] = React.useState<string | null>(null)

  const views = [
    <></>,
    <ListExams key="list-exam" classId={classId} onSelectExam={(examId: any) => {
      setSelectedExam(examId)
      setCurrentView(2)
    }} />,
    <ListExamPapers key="exam-papers" examId={selectedExam} classId={classId} />,
    <></>
  ]

  useEffect(() => {
    const examId = searchParams.get('examId')
    if (examId) {
      setSelectedExam(examId)
      setCurrentView(2)
    } else {
      setCurrentView(1)
    }
  }, [searchParams])

  return (
    <div className="h-full">
      <div className="mb-4">
        <h2 className="text-2xl font-bold text-gray-900">
          {t('exam.title')}
        </h2>
      </div>
      {views[currentView]}
    </div>
  )
}

export default ExamTab